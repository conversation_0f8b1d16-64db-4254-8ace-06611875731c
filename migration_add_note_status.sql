-- Migration to add note_status column to notes table
-- Run this SQL script in your database to add the note_status column

-- Add the note_status column with default value 0 (TO-DO)
ALTER TABLE notes ADD COLUMN IF NOT EXISTS note_status INTEGER NOT NULL DEFAULT 0;

-- Update any existing notes to have status 0 (TO-DO) if they don't already have a status
UPDATE notes SET note_status = 0 WHERE note_status IS NULL;

-- Verify the migration
SELECT 
    note_id, 
    note_title, 
    note_status,
    note_created_at
FROM notes 
ORDER BY note_created_at DESC 
LIMIT 5;
