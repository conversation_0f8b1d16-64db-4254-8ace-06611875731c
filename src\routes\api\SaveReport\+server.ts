import { json } from '@sveltejs/kit';
import { saveReport } from '$lib/services/reports';
import { isEmail } from '$lib/utils/validate';
import { isValidURL } from '$lib/utils/validation';
import type { RequestHand<PERSON> } from './$types';
import type { SaveReportRequest } from '$lib/types/requests';
import type { SaveReportResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const reportRequest = await request.json() as SaveReportRequest;

        if (!reportRequest.report_website_url) {
            return json({
                status: "error",
                message: "Report website URL is required"
            } as SaveReportResponse, { status: 400 });
        }

        if (!isValidURL(reportRequest.report_website_url)) {
            return json({
                status: "error",
                message: "Invalid website URL format"
            } as SaveReportResponse, { status: 400 });
        }

        if (reportRequest.report_email_address && !isEmail(reportRequest.report_email_address)) {
            return json({
                status: "error",
                message: "Invalid email address"
            } as SaveReportResponse, { status: 400 });
        }

        const report = await saveReport(
            reportRequest.report_id,
            reportRequest.report_website_url,
            reportRequest.report_email_address || '',
            reportRequest.report_email_password || '',
            reportRequest.report_status,
            reportRequest.report_password
        );

        const isUpdate = !!reportRequest.report_id;
        const message = isUpdate ? "Report updated successfully" : "Report created successfully";

        return json({
            status: "success",
            message,
            report
        } as SaveReportResponse, { status: isUpdate ? 200 : 201 });
    } catch (error) {
        console.error('Unexpected error at SaveReport:', error);
        return json({
            status: "error",
            message: `Unexpected error at SaveReport: ${error instanceof Error ? error.message : 'Unknown error'}`
        } as SaveReportResponse, { status: 500 });
    }
}
