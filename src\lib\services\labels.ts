import sql from '$lib/db/db';
import type { Label } from '$lib/types/tables';
import { generateUUID } from '$lib/utils/uuid';

export async function getLabels(): Promise<Label[]> {
    const labels = await sql<Label[]>`
        SELECT
            label_id,
            label_name,
            label_group,
            label_color,
            label_order
        FROM labels
        ORDER BY label_order DESC, label_name ASC
    `;

    return labels;
}

export async function getLabelIdByName(name: string): Promise<string | null> {
    const result = await sql<{ label_id: string }[]>`
        SELECT label_id
        FROM labels
        WHERE label_name = ${name}
        LIMIT 1
    `;

    return result.length > 0 ? result[0].label_id : null;
}

export async function saveLabel(label_id: string | undefined, label_name: string, label_group: string, label_color?: string, label_order?: number): Promise<Label> {
    const id = label_id || generateUUID();

    const result = await sql<Label[]>`
        INSERT INTO labels (label_id, label_name, label_group, label_color, label_order)
        VALUES (${id}, ${label_name}, ${label_group}, ${label_color || '#000000'}, ${label_order || 0})
        ON CONFLICT (label_id)
        DO UPDATE SET
            label_name = EXCLUDED.label_name,
            label_group = EXCLUDED.label_group,
            label_color = EXCLUDED.label_color,
            label_order = EXCLUDED.label_order
        RETURNING *
    `;

    return result[0];
}

export async function deleteLabel(labelId: string): Promise<void> {
    // First delete any relationships with sites
    await sql`
        DELETE FROM site_label_rels
        WHERE label_id = ${labelId}
    `;

    // Then delete the label
    const result = await sql`
        DELETE FROM labels
        WHERE label_id = ${labelId}
        RETURNING label_id
    `;

    if (result.length === 0) {
        throw new Error('No label found with the given ID');
    }
}