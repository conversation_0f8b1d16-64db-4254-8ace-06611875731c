import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { v4 as uuidv4 } from 'uuid';
import { PUBLIC_IMAGES_FOLDER, PUBLIC_SITE_URL } from '$lib/utils/env';
import * as fs from 'node:fs/promises';
import * as path from 'node:path';

interface Base64ImageRequest {
    base64: string;
}

// Handle OPTIONS requests for CORS preflight
export const OPTIONS: RequestHandler = async () => {
    return new Response(null, { status: 204 });
};

export const POST: RequestHandler = async ({ request }) => {
    try {
        const data = await request.json() as Base64ImageRequest;

        if (!data.base64) {
            return json({
                status: 'error',
                message: 'Base64 image data is required'
            }, { status: 400 });
        }

        // Remove data:image/png;base64, prefix if present
        const base64Data = data.base64.replace(/^data:image\/png;base64,/, '');

        // Convert base64 to buffer
        const imageBuffer = Buffer.from(base64Data, 'base64');

        // Check file size (4MB limit)
        if (imageBuffer.length > 4194304) {
            return json({
                status: 'error',
                message: 'Image size must be less than 4MB'
            }, { status: 400 });
        }

        const fileName = `${uuidv4()}.png`;

        // Determine the full path to save the file
        const imagePath = path.join(PUBLIC_IMAGES_FOLDER, fileName);

        try {
            // Ensure the directory exists
            await fs.mkdir(PUBLIC_IMAGES_FOLDER, { recursive: true });

            // Write the file
            await fs.writeFile(imagePath, imageBuffer);
        } catch (writeError) {
            console.error('Error writing file:', writeError);
            return json({
                status: 'error',
                message: 'Failed to save image to server'
            }, { status: 500 });
        }

        // For the URL, we need to use the correct path that the web server exposes
        // In production, images are served from /images/ even though they're stored in /home/<USER>/client/images
        const imageUrl = `${PUBLIC_SITE_URL}/images/${fileName}`;

        return json({
            status: 'success',
            message: 'Image uploaded successfully',
            image_url: imageUrl
        }, { status: 200 });

    } catch (error) {
        console.error('Error in UploadBase64Image:', error);
        return json({
            status: 'error',
            message: 'An error occurred while processing your request'
        }, { status: 500 });
    }
};