<script lang="ts">
    import type { Note } from '$lib/types/tables';
    import type { UpdateNoteStatusResponse } from '$lib/types/responses';
    import KanbanColumn from './KanbanColumn.svelte';
    import { toast } from 'svelte-sonner';

    interface Props {
        notes: Note[];
        onEdit: (note: Note) => void;
        onDelete: (noteId: string) => void;
        onNotesUpdate: () => Promise<void>;
    }

    let { notes, onEdit, onDelete, onNotesUpdate }: Props = $props();

    const columns = [
        { id: 0, title: 'TO-DO', status: 0 },
        { id: 1, title: 'TO-REVIEW', status: 1 },
        { id: 2, title: 'DONE', status: 2 },
        { id: 3, title: 'SAVED', status: 3 }
    ];

    let activeNote: Note | null = $state(null);

    function getNotesByStatus(status: number): Note[] {
        return notes.filter(note => note.note_status === status);
    }

    async function updateNoteStatus(noteId: string, newStatus: number): Promise<boolean> {
        try {
            const response = await fetch('/api/UpdateNoteStatus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    note_id: noteId,
                    note_status: newStatus
                })
            });

            const data = await response.json() as UpdateNoteStatusResponse;

            if (data.status === 'success') {
                toast.success('Note status updated successfully');
                return true;
            } else {
                toast.error(data.message || 'Failed to update note status');
                return false;
            }
        } catch (error) {
            console.error('Error updating note status:', error);
            toast.error('Failed to update note status');
            return false;
        }
    }

    function handleDragStart(note: Note) {
        activeNote = note;
    }

    async function handleDrop(noteId: string, newStatus: number) {
        const note = notes.find(n => n.note_id === noteId);
        if (!note || note.note_status === newStatus) return;

        const success = await updateNoteStatus(noteId, newStatus);
        if (success) {
            await onNotesUpdate();
        }
        activeNote = null;
    }
</script>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 h-full">
    {#each columns as column}
        <KanbanColumn
            title={column.title}
            status={column.status}
            notes={getNotesByStatus(column.status)}
            {onEdit}
            {onDelete}
            onDrop={handleDrop}
            onDragStart={handleDragStart}
        />
    {/each}
</div>


