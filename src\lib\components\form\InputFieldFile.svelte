<script lang="ts">
    import { toast } from 'svelte-sonner';
    import type { UploadImageResponse } from '$lib/types/responses';

    let {
        name = '',
        label = '',
        required = false,
        value = $bindable(''),
        error = $bindable(''),
        isExampleMode = false
    } = $props();

    let isUploading = $state(false);
    let fileInput = $state<HTMLInputElement>();

    function handleFileSelect(event: Event): void {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];
        if (file && file.type.startsWith('image/')) {
            isUploading = true;
            uploadImage(file);
            if (fileInput) fileInput.value = '';
        }
    }

    async function uploadImage(file: File): Promise<void> {
        try {
            console.log('Preparing to upload image:', {
                name: file.name,
                type: file.type,
                size: file.size
            });

            const formData = new FormData();
            formData.append('image', file);

            const response = await fetch('/api/UploadImage', {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                toast.error('Failed to upload image');
                isUploading = false;
                return;
            }

            const result = await response.json() as UploadImageResponse;

            if (result.status === 'success') {
                console.log("Upload successful. Image URL:", result.image_url);
                value = result.image_url;
                if (error) {
                    error = '';
                }
                toast.success('Image uploaded successfully');
            } else {
                console.error('Upload returned error:', result.message);
                toast.error(result.message || 'Failed to upload image');
            }
        } catch (error) {
            console.error('Error uploading image:', error);
            toast.error('Error uploading image: ' + (error instanceof Error ? error.message : String(error)));
        } finally {
            isUploading = false;
        }
    }

    function removeImage(): void {
        value = '';
    }
</script>

<div class="sm:col-span-2">
    {#if label !== ""}
        <p class="{error ? 'text-red-600' : 'text-black'} mb-[10px] block text-sm font-semibold md:text-base">
            {label}
            {#if required}
                <span>*</span>
            {:else}
                <i class="text-xs font-normal">(optional)</i>
            {/if}
        </p>
    {/if}
    <div class="file-upload-container" id={`file-container-${name}`}>
        {#if value}
          <div class="flex justify-center rounded-lg border {error ? 'border-red-600 bg-red-100/50' : 'border-gray-300'} relative">
            <div class="flex flex-col items-center justify-center py-5 h-40">
              <img src={value} class="max-h-40" alt="" />
            </div>
            <button
              type="button"
              class="remove-image-btn text-xs text-white px-2 py-1 bg-red-600 rounded absolute top-2 right-2"
              onclick={removeImage}
            >
              Remove Image
            </button>
          </div>
        {:else}
          <div
            class="flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed {error ? 'border-red-600 bg-red-100/50' : 'border-gray-400 bg-white'} relative"
          >
            <div class="flex flex-col items-center justify-center py-5 h-40">
              <svg class="mb-2 h-12 w-12 fill-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><path d="M144 480C64.5 480 0 415.5 0 336c0-62.8 40.2-116.2 96.2-135.9c-.1-2.7-.2-5.4-.2-8.1c0-88.4 71.6-160 160-160c59.3 0 111 32.2 138.7 80.2C409.9 102 428.3 96 448 96c53 0 96 43 96 96c0 12.2-2.3 23.8-6.4 34.6C596 238.4 640 290.1 640 352c0 70.7-57.3 128-128 128l-368 0zm79-217c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l39-39L296 392c0 13.3 10.7 24 24 24s24-10.7 24-24l0-134.1 39 39c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-80-80c-9.4-9.4-24.6-9.4-33.9 0l-80 80z"/></svg>
              {#if isExampleMode}
                <p class="mb-2 text-sm text-gray-500 text-center">
                  <span class="font-semibold">Image uploads disabled</span>
                  <br>in example mode
                </p>
              {:else}
                <p class="mb-2 text-sm text-gray-500 text-center">
                  <span class="font-semibold">Click to upload</span>
                  or drag and drop
                </p>
                <p class="text-xs font-semibold text-gray-500">Max. File Size: 30MB</p>
              {/if}
            </div>
            <input
              type="file"
              accept="image/png, image/jpeg"
              onchange={handleFileSelect}
              bind:this={fileInput}
              class="cursor-pointer block opacity-0 h-full w-full left-0 top-0 absolute z-10 file-upload-input"
              data-field-name={name}
              disabled={isExampleMode}
            />
            <input
              {name}
              type="hidden"
              class="hidden file-path-input"
              bind:value
              {required}
            />
          </div>
        {/if}
    </div>
    {#if error}
        <p class="mt-2 text-sm text-red-600">{error}</p>
    {/if}
</div>
