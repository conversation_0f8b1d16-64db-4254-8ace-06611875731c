<script lang="ts">
  import type { PageProps } from './$types';
  import { toast } from 'svelte-sonner';
  import type { Label, Report } from '$lib/types/tables';
  import type { ReportSiteWithLabels } from '$lib/types/services';
  import type { GetReportSitesRequest } from '$lib/types/requests';
  import { formatVisits } from '$lib/utils/format';
  import { onMount } from 'svelte';

  let { data }: PageProps = $props();
  let report = data.report as Report;
  let isAuthenticated = $state(data.isAuthenticated || false);

  let reportSites = $state<ReportSiteWithLabels[]>([]);
  let labels = $state<Label[]>([]);
  let isLoading = $state(true);
  let checkedCount = $derived.by(() => {
    return reportSites.filter(site => site.checked).length;
  });

  // Password authentication state
  let password = $state('');
  let passwordError = $state('');
  let isAuthenticating = $state(false);

  async function authenticateReport() {
    if (!password.trim()) {
      passwordError = 'Password is required';
      return;
    }

    isAuthenticating = true;
    passwordError = '';

    try {
      const response = await fetch('/api/AuthenticateReport', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          report_id: report.report_id,
          password: password
        })
      });

      const result = await response.json();

      if (result.status === 'success') {
        isAuthenticated = true;
        isLoading = true;
        toast.success('Authentication successful');
        // Load report data after successful authentication
        await fetchLabels();
        await getReportSites();
      } else {
        passwordError = result.message || 'Invalid password';
      }
    } catch (error) {
      console.error('Authentication error:', error);
      passwordError = 'An error occurred during authentication';
    } finally {
      isAuthenticating = false;
    }
  }

  async function generateReport() {
    try {
      if (checkedCount === 0) {
        toast.error('Please select at least one site to generate a report');
        return;
      }

      const response = await fetch('/api/GenerateReport', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          report_id: report.report_id
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        toast.error(errorData.message || 'Failed to generate report');
        return;
      }

      // Get the filename from the Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition');
      const filenameMatch = contentDisposition?.match(/filename="(.+)"/);
      const filename = filenameMatch ? filenameMatch[1] : `report_${report.report_id}.xlsx`;

      // Convert the response to a blob
      const blob = await response.blob();

      // Create a download link and trigger the download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('Report generated successfully');
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('Failed to generate report');
    }
  }

  async function fetchLabels() {
    try {
      const response = await fetch('/api/GetLabels', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();

      if (data.status === 'success') {
        labels = data.labels || [];
      }
    } catch (error) {
      console.error('Error fetching labels:', error);
    }
  }

  async function getReportSites(): Promise<void> {
    try {
      const body: GetReportSitesRequest = {
        report_id: report.report_id,
        label_ids: [],
        page_num: 1,
        per_page: 500,
        sort: 'dr'
      };
      const sitesResponse = await fetch('/api/GetReportSites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      });

      const sitesData = await sitesResponse.json();

      if (sitesData.status === 'success') {
        reportSites = sitesData.report_sites || [];
        reportSites = reportSites.filter(site => site.checked === true);
        console.log("reportSites: ", reportSites);
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast.error('Failed to load report data');
    } finally {
      isLoading = false;
    }
  }

  // Load data on mount
  onMount(async () => {
    if (isAuthenticated) {
      await fetchLabels();
      await getReportSites();
    } else {
      isLoading = false;
    }
  });

  function getLabelsForGroup(site: ReportSiteWithLabels, group: string) {
    return labels.filter(label =>
      label.label_group === group &&
      site.label_ids.includes(label.label_id)
    );
  }
</script>

<section class="flex-1 bg-secondary py-8 lg:py-12">
  {#if !isAuthenticated}
    <div class="container">
      <div class="flex items-center justify-center min-h-[60vh]">
        <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
          <h2 class="text-2xl font-bold mb-6 text-center">Report Access</h2>
          <p class="text-gray-600 mb-6 text-center">Please enter the password to access this report.</p>

          <form onsubmit={(e) => { e.preventDefault(); authenticateReport(); }}>
            <div class="mb-4">
              <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
              <input
                type="password"
                id="password"
                bind:value={password}
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter report password"
                disabled={isAuthenticating}
              />
              {#if passwordError}
                <p class="text-red-600 text-sm mt-1">{passwordError}</p>
              {/if}
            </div>

            <button
              type="submit"
              disabled={isAuthenticating}
              class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isAuthenticating ? 'Authenticating...' : 'Access Report'}
            </button>
          </form>
        </div>
      </div>
    </div>
  {:else if isLoading}
    <div class="container">
      <div class="flex items-center justify-center h-64">
        <div class="flex flex-col items-center gap-4">
          <div class="h-12 w-12 animate-spin rounded-full border-4 border-gray-200 border-t-blue-500"></div>
          <p class="text-white font-semibold">Loading report data...</p>
        </div>
      </div>
    </div>
  {:else if reportSites.length === 0}
    <div class="container">
      <div class="flex items-center justify-center h-64">
        <div class="bg-white p-8 rounded-lg shadow-lg">
          <h2 class="text-2xl font-bold mb-4">No report data found</h2>
          <p class="text-gray-600">This report doesn't have any sites or the report doesn't exist.</p>
        </div>
      </div>
    </div>
  {:else}
    <div class="container">
      <div class="mb-12 flex items-start justify-between gap-2 md:gap-4">
        <div class="flex items-left md:items-center gap-2 md:gap-4 flex-col md:flex-row">
          <h1 class="text-3xl font-bold text-white md:text-3xl lg:text-4xl mb-0">Report</h1>
          {#if report.report_status === 1}
            <div class="w-fit text-xs md:text-sm text-white font-semibold uppercase bg-green-600 px-4 py-2 rounded">Completed</div>
          {:else if report.report_status === 2}
            <div class="w-fit text-xs md:text-sm text-white font-semibold uppercase bg-blue-600 px-4 py-2 rounded">Sent</div>
          {:else}
            <div class="w-fit text-xs md:text-sm text-white font-semibold uppercase bg-secondary-hover px-4 py-2 rounded">In Progress</div>
          {/if}
        </div>
        <button id="generate-report" class="btn-primary w-fit gap-0" onclick={generateReport}>
          Generate XLSX ({checkedCount})
        </button>
      </div>

      <div class="overflow-hidden rounded-lg bg-white ring-4 ring-white/25 mb-8">
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 p-4">
          <div class="relative">
            <div class="absolute -top-[8px] left-2 bg-white px-1 text-xs font-bold text-black">Website URL:</div>
            <input type="text" class="flex w-full appearance-none items-center justify-between rounded border border-gray-400 bg-transparent p-3 text-sm text-black hover:border-gray-600 focus:border-gray-600 focus:outline-none" value={report.report_website_url} readonly />
          </div>
          <div class="relative">
            <div class="absolute -top-[8px] left-2 bg-white px-1 text-xs font-bold text-black">Email Address:</div>
            <input type="text" class="flex w-full appearance-none items-center justify-between rounded border border-gray-400 bg-transparent p-3 text-sm text-black hover:border-gray-600 focus:border-gray-600 focus:outline-none" value={report.report_email_address} readonly />
          </div>
          <div class="relative">
            <div class="absolute -top-[8px] left-2 bg-white px-1 text-xs font-bold text-black">Email Password:</div>
            <input type="text" class="flex w-full appearance-none items-center justify-between rounded border border-gray-400 bg-transparent p-3 text-sm text-black hover:border-gray-600 focus:border-gray-600 focus:outline-none" value={report.report_email_password} readonly />
          </div>
        </div>
      </div>

      <div class="overflow-hidden rounded-lg bg-white ring-4 ring-white/25">
        <div class="overflow-x-auto">
          <div class="overflow-auto">
            <table class="table-fixed w-full text-left text-sm font-semibold text-gray-500">
              <thead class="bg-white text-xs uppercase text-gray-600">
                <tr class="border-b border-gray-300">
                  <td class="w-[60px] whitespace-nowrap py-4 pl-4 pr-2">#</td>
                  <td class="w-[240px] whitespace-nowrap px-2 py-4">Name</td>
                  <td class="w-[250px] whitespace-nowrap px-2 py-4">Screenshots</td>
                  <td class="w-[50px] whitespace-nowrap px-2 py-4">DR</td>
                  <td class="w-[80px] whitespace-nowrap px-2 py-4">Visits</td>
                  <td class="w-[160px] whitespace-nowrap px-2 py-4">Type</td>
                  <td class="w-[100px] whitespace-nowrap px-2 py-4">Pricing</td>
                  <td class="w-[140px] whitespace-nowrap px-2 py-4">Link Type</td>
                  <td class="w-[100px] whitespace-nowrap px-2 py-4">URL</td>
                  <td class="w-[100px] whitespace-nowrap px-2 pl-2 pr-4">Submit URL</td>
                </tr>
              </thead>

              <tbody class="text-sm text-black">
                {#each reportSites as site, i}
                  <tr class="border-b border-gray-300 hover:bg-gray-200">
                    <td class="py-2 pl-4 pr-2">{i + 1}</td>
                    <td class="truncate whitespace-nowrap p-2">{site.site_name}</td>
                    <td class="p-2">
                      <div class="flex flex-wrap gap-2">
                        {#each site.screenshot_urls as screenshotUrl, index}
                          <button
                            type="button"
                            class="relative group h-10 w-10 rounded border border-gray-300 overflow-hidden hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            onclick={() => window.open(screenshotUrl, '_blank')}
                            aria-label="View screenshot {index + 1}"
                          >
                            <img
                              src={screenshotUrl}
                              alt="Screenshot {index + 1}"
                              class="h-full w-full object-cover"
                            />
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                              <svg class="h-4 w-4 fill-white opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <use href="#icon-external"></use>
                              </svg>
                            </div>
                          </button>
                        {/each}
                        {#if site.screenshot_urls.length === 0}
                          <span class="text-gray-400 text-sm">No screenshots</span>
                        {/if}
                      </div>
                    </td>
                    <td class="p-2">{site.site_dr?.toString() || ''}</td>
                    <td class="p-2">{formatVisits(site.site_visits)}</td>

                    <td class="p-2">
                      {#each getLabelsForGroup(site, 'type') as label}
                        <div
                          style={`background: ${label.label_color}1a; border-color: ${label.label_color}; color: ${label.label_color}`}
                          class="rounded-2xl font-semibold border text-xs px-3 py-1 w-fit whitespace-nowrap"
                        >
                          {label.label_name}
                        </div>
                      {/each}
                    </td>
                    <td class="p-2">
                      {#each getLabelsForGroup(site, 'pricing') as label}
                        <div
                          style={`background: ${label.label_color}1a; border-color: ${label.label_color}; color: ${label.label_color}`}
                          class="rounded-2xl font-semibold border text-xs px-3 py-1 w-fit whitespace-nowrap"
                        >
                          {label.label_name}
                        </div>
                      {/each}
                    </td>
                    <td class="p-2">
                      {#each getLabelsForGroup(site, 'link-type') as label}
                        <div
                          style={`background: ${label.label_color}1a; border-color: ${label.label_color}; color: ${label.label_color}`}
                          class="rounded-2xl font-semibold border text-xs px-3 py-1 w-fit whitespace-nowrap"
                        >
                          {label.label_name}
                        </div>
                      {/each}
                    </td>
                    <td class="p-2">
                      <a class="flex w-fit items-center gap-2 text-black hover:text-secondary hover:underline" href={site.site_url} target="_blank" rel="noopener noreferrer">
                        Visit
                        <svg class="h-3 w-3 fill-current" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><use href="#icon-external"></use></svg>
                      </a>
                    </td>
                    <td class="py-2 pl-2 pr-4">
                      <a class="flex w-fit items-center gap-2 text-black hover:text-secondary hover:underline" href={site.site_submit_url} target="_blank" rel="noopener noreferrer">
                        Submit
                        <svg class="h-3 w-3 fill-current" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><use href="#icon-external"></use></svg>
                      </a>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  {/if}
</section>
