import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { v4 as uuidv4 } from 'uuid';
import { PUBLIC_SITE_URL, PUBLIC_FILES_FOLDER } from '$lib/utils/env';
import * as fs from 'node:fs/promises';
import * as path from 'node:path';

export const OPTIONS: RequestHandler = async () => {
    return new Response(null, { status: 204 });
};

export const POST: RequestHandler = async ({ request }) => {
    try {
        const formData = await request.formData();
        const file = formData.get('file');

        if (!file || !(file instanceof File)) {
            return json({
                status: 'error',
                message: 'Unable to read file'
            }, { status: 400 });
        }

        if (file.size > 10485760) {
            return json({
                status: 'error',
                message: 'File size must be less than 10MB'
            }, { status: 400 });
        }

        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv',
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'application/zip',
            'application/x-zip-compressed'
        ];

        if (!allowedTypes.includes(file.type)) {
            return json({
                status: 'error',
                message: `Unsupported file type: ${file.type}. Allowed types include PDF, Word, Excel, PowerPoint, images, text files, and ZIP archives.`
            }, { status: 400 });
        }

        const fileBuffer = Buffer.from(await file.arrayBuffer());
        const fileName = `${uuidv4()}${path.extname(file.name)}`;

        const filePath = path.join(PUBLIC_FILES_FOLDER, fileName);

        try {
            await fs.mkdir(PUBLIC_FILES_FOLDER, { recursive: true });
            await fs.writeFile(filePath, fileBuffer);
        } catch (writeError) {
            console.error('Error writing file:', writeError);
            return json({
                status: 'error',
                message: 'Failed to save file to server'
            }, { status: 500 });
        }

        const fileUrl = `${PUBLIC_SITE_URL}/files/${fileName}`;

        return json({
            status: 'success',
            message: 'File uploaded successfully',
            file_url: fileUrl,
            file_name: file.name,
            file_size: file.size,
            file_type: file.type
        });

    } catch (error) {
        console.error('Error in UploadFile:', error);
        return json({
            status: 'error',
            message: 'An error occurred while processing your request'
        }, { status: 500 });
    }
};
