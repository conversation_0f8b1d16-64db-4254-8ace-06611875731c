import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import sql from '$lib/db/db';
import type { BaseResponse } from '$lib/types/responses';
import { getLabelIdByName } from '$lib/services/labels';

export interface ValidateFormFieldsJSONRequest {
    onlyApproved?: boolean;
}

export interface ValidateFormFieldsJSONResponse extends BaseResponse {
    results: Array<{
        site_id: string;
        site_name: string;
        site_url: string;
        status: 'valid' | 'invalid' | 'empty';
        error?: string;
        fieldCount?: number;
    }>;
}

export const POST: RequestHandler = async ({ request }) => {
    try {
        const body = await request.json() as ValidateFormFieldsJSONRequest;
        const onlyApproved = body.onlyApproved ?? true; // Default to true to only check approved sites

        let sites;

        if (onlyApproved) {
            // Get the "Approved" label ID
            const approvedLabelId = await getLabelIdByName('Approved');

            if (!approvedLabelId) {
                return json({
                    status: 'error',
                    message: 'Approved label not found in the system',
                    results: []
                } as ValidateFormFieldsJSONResponse, { status: 400 });
            }

            // Query sites with "Approved" label only
            sites = await sql`
                SELECT s.site_id, s.site_name, s.site_url, s.site_form_fields
                FROM sites s
                INNER JOIN site_label_rels slr ON s.site_id = slr.site_id
                WHERE slr.label_id = ${approvedLabelId}
                ORDER BY s.site_name
            `;
        } else {
            // Query all sites (original behavior)
            sites = await sql`
                SELECT site_id, site_name, site_url, site_form_fields
                FROM sites
                ORDER BY site_name
            `;
        }

        const results = sites.map(site => {
            const result = {
                site_id: site.site_id,
                site_name: site.site_name || '',
                site_url: site.site_url || '',
                status: 'empty' as 'valid' | 'invalid' | 'empty',
                error: undefined as string | undefined,
                fieldCount: undefined as number | undefined
            };

            if (!site.site_form_fields) {
                result.status = 'empty';
                result.fieldCount = 0;
                return result;
            }

            try {
                let parsed;

                if (typeof site.site_form_fields === 'string') {
                    parsed = JSON.parse(site.site_form_fields);
                } else {
                    // If it's already an object, it's valid
                    parsed = site.site_form_fields;
                }

                if (Array.isArray(parsed)) {
                    result.status = 'valid';
                    result.fieldCount = parsed.length;
                } else if (parsed === null || parsed === undefined) {
                    result.status = 'empty';
                    result.fieldCount = 0;
                } else {
                    result.status = 'invalid';
                    result.error = 'Not an array';
                }
            } catch (error) {
                result.status = 'invalid';
                result.error = error instanceof Error ? error.message : 'Invalid JSON';
            }

            return result;
        });

        return json({
            status: 'success',
            message: 'JSON validation completed',
            results
        } as ValidateFormFieldsJSONResponse, { status: 200 });
    } catch (error) {
        console.error('Unexpected error at ValidateFormFieldsJSON:', error);
        return json({
            status: 'error',
            message: `Unexpected error at ValidateFormFieldsJSON: ${error instanceof Error ? error.message : 'Unknown error'}`,
            results: []
        } as ValidateFormFieldsJSONResponse, { status: 500 });
    }
}
