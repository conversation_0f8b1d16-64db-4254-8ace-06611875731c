import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { reportExists } from '$lib/services/reports';

export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const { report_id, password } = await request.json();

    if (!report_id || !password) {
      return json({
        status: 'error',
        message: 'Report ID and password are required'
      }, { status: 400 });
    }

    const report = await reportExists(report_id);
    
    if (!report) {
      return json({
        status: 'error',
        message: 'Report not found'
      }, { status: 404 });
    }

    if (report.report_password !== password) {
      return json({
        status: 'error',
        message: 'Invalid password'
      }, { status: 401 });
    }

    // Set password cookie for this report (expires in 24 hours)
    cookies.set(`report_password_${report_id}`, password, {
      path: '/',
      httpOnly: true,
      sameSite: 'strict',
      secure: process.env.NODE_ENV === 'production',
      maxAge: 60 * 60 * 24 // 24 hours
    });

    return json({
      status: 'success',
      message: 'Authentication successful'
    });
  } catch (error) {
    console.error('Unexpected error at AuthenticateReport:', error);
    return json({
      status: 'error',
      message: 'An unexpected error occurred'
    }, { status: 500 });
  }
};
