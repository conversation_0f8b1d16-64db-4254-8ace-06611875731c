// Check if we're in production mode
const isProd = import.meta.env.PROD;

// Environment-specific values
let PUBLIC_SITE_URL: string;
let PUBLIC_SITE_OGIMAGE: string;
let PUBLIC_IMAGES_FOLDER: string;
let PUBLIC_FILES_FOLDER: string;

if (isProd) {
    PUBLIC_SITE_URL = 'https://submitsaas.com';
    PUBLIC_SITE_OGIMAGE = 'https://submitsaas.com/assets/images/ogimage.png';
    PUBLIC_IMAGES_FOLDER = '/home/<USER>/images';
    PUBLIC_FILES_FOLDER = '/home/<USER>/files';
} else {
    PUBLIC_SITE_URL = 'http://localhost:5173';
    PUBLIC_SITE_OGIMAGE = 'http://localhost:5173/assets/images/ogimage.png';
    PUBLIC_IMAGES_FOLDER = 'static/images';
    PUBLIC_FILES_FOLDER = 'static/files';
}

// Constants
export const PUBLIC_SITE_NAME = 'SubmitSaaS';
export const PUBLIC_SITE_TAGLINE = 'Submit your SaaS to 100+ Directories';
export const PUBLIC_SITE_DESCRIPTION = 'Increase your SaaS visibility by submitting to 100+ directories. Save time, gain high-quality backlinks, and grow your reach with minimal effort.';
export const PUBLIC_SITE_ABOUT = 'SubmitSaaS is a service that submits any SaaS to 100+ directories.';
export const TABLE_LAST_UPDATE = 'May 24, 2025';
export { PUBLIC_SITE_URL, PUBLIC_SITE_OGIMAGE, PUBLIC_IMAGES_FOLDER, PUBLIC_FILES_FOLDER, isProd };
