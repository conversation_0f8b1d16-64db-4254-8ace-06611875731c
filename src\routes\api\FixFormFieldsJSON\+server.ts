import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import sql from '$lib/db/db';
import type { BaseResponse } from '$lib/types/responses';

export interface FixFormFieldsJSONRequest {
    site_id: string;
}

export interface FixFormFieldsJSONResponse extends BaseResponse {
    // No additional properties
}

export const POST: RequestHandler = async ({ request }) => {
    try {
        const body = await request.json() as FixFormFieldsJSONRequest;
        const { site_id } = body;

        if (!site_id) {
            return json({
                status: 'error',
                message: 'Site ID is required'
            } as FixFormFieldsJSONResponse, { status: 400 });
        }

        // Set the site_form_fields to NULL (empty)
        const result = await sql`
            UPDATE sites
            SET site_form_fields = NULL
            WHERE site_id = ${site_id}
            RETURNING site_id
        `;

        if (result.length === 0) {
            return json({
                status: 'error',
                message: 'Site not found'
            } as FixFormFieldsJSONResponse, { status: 404 });
        }

        return json({
            status: 'success',
            message: 'Form fields JSON fixed successfully (set to empty)'
        } as FixFormFieldsJSONResponse, { status: 200 });
    } catch (error) {
        console.error('Unexpected error at FixFormFieldsJSON:', error);
        return json({
            status: 'error',
            message: `Unexpected error at FixFormFieldsJSON: ${error instanceof Error ? error.message : 'Unknown error'}`
        } as FixFormFieldsJSONResponse, { status: 500 });
    }
}
