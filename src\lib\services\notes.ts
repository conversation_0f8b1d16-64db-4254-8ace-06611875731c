import sql from '$lib/db/db';
import type { Note } from '$lib/types/tables';
import { generateUUID } from '$lib/utils/uuid';
import { deleteFilesFromSystem, parseNoteFiles } from '$lib/utils/fileUtils';

export async function getNotes(): Promise<Note[]> {
    const notes = await sql<Note[]>`
        SELECT
            note_id,
            note_title,
            note_content,
            note_created_at,
            note_files
        FROM notes
        ORDER BY note_created_at DESC
    `;

    return notes;
}

export async function saveNote(note_id: string | undefined, note_title: string, note_content: string, note_files?: string): Promise<Note> {
    const currentTime = Math.floor(Date.now() / 1000);
    const id = note_id || generateUUID();

    try {
        // If this is an update (note_id exists), handle file cleanup
        if (note_id) {
            // Get the existing note to compare files
            const existingNoteResult = await sql<Note[]>`
                SELECT note_files
                FROM notes
                WHERE note_id = ${note_id}
            `;

            if (existingNoteResult.length > 0) {
                const existingNote = existingNoteResult[0];
                const existingFiles = parseNoteFiles(existingNote.note_files);
                const newFiles = parseNoteFiles(note_files);

                // Find files that are in the existing note but not in the new note
                const filesToDelete = existingFiles.filter(existingFile =>
                    !newFiles.some(newFile => newFile.file_url === existingFile.file_url)
                );

                // Delete orphaned files from the file system
                if (filesToDelete.length > 0) {
                    const deletedCount = await deleteFilesFromSystem(filesToDelete);
                    console.log(`Cleaned up ${deletedCount} out of ${filesToDelete.length} orphaned files for note ${note_id}`);
                }
            }
        }

        // Save the note
        const result = await sql<Note[]>`
            INSERT INTO notes (note_id, note_title, note_content, note_created_at, note_files)
            VALUES (${id}, ${note_title}, ${note_content}, ${currentTime}, ${note_files || '[]'}::json)
            ON CONFLICT (note_id)
            DO UPDATE SET
                note_title = EXCLUDED.note_title,
                note_content = EXCLUDED.note_content,
                note_files = EXCLUDED.note_files
            RETURNING *
        `;

        return result[0];
    } catch (error) {
        console.error('Error saving note:', error);
        throw error;
    }
}

export async function deleteNote(note_id: string): Promise<boolean> {
    try {
        // First, get the note to retrieve its files
        const noteResult = await sql<Note[]>`
            SELECT note_files
            FROM notes
            WHERE note_id = ${note_id}
        `;

        if (noteResult.length === 0) {
            return false; // Note not found
        }

        const note = noteResult[0];

        // Parse and delete associated files from the file system
        const files = parseNoteFiles(note.note_files);
        if (files.length > 0) {
            const deletedCount = await deleteFilesFromSystem(files);
            console.log(`Deleted ${deletedCount} out of ${files.length} files for note ${note_id}`);
        }

        // Delete the note from the database
        const deleteResult = await sql`
            DELETE FROM notes
            WHERE note_id = ${note_id}
        `;

        return deleteResult.count > 0;
    } catch (error) {
        console.error('Error deleting note:', error);
        return false;
    }
}

export async function noteExists(note_id: string): Promise<boolean> {
    const result = await sql`
        SELECT EXISTS(
            SELECT 1
            FROM notes
            WHERE note_id = ${note_id}
            LIMIT 1
        )
    `;

    return result[0].exists;
}
