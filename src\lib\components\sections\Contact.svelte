<script lang="ts">
    let decodedEmail = false;
    
    function decodeFromBase64(text: string): string {
        if (typeof window !== 'undefined') {
            try {
                return atob(text);
            } catch (e) {
                console.error("Error decoding base64:", e);
                return "";
            }
        }
        return '';
    }

    function encodeToBase64(text: string): string {
        if (typeof window !== 'undefined') {
            try {
                return btoa(text);
            } catch (e) {
                console.error("Error encoding to base64:", e);
                return "";
            }
        }
        return ''; // Fallback for SSR
    }
    
    function handleEmailInteraction(event: MouseEvent | FocusEvent) {
        if (!decodedEmail) {
            const element = event.currentTarget as HTMLAnchorElement;
            const encoded = element.getAttribute('href') || '';
            element.href = decodeFromBase64(encoded);
            decodedEmail = true;
        }
    }
</script>

<section id="section-contact" class="py-8 lg:py-24 bg-[#ddeeff]">
    <div class="container">
        <h2 class="max-w-3xl mx-auto mb-4 text-4xl font-bold text-center text-black lg:text-5xl">Contact Us</h2>
        <p class="max-w-3xl mx-auto text-center text-gray-600 text-lg sm:text-2xl mb-8 lg:mb-16">If you have any questions, we're here to help!</p>
        <div class="items-center gap-12 md:flex max-w-[840px] mx-auto">
            <div class="w-full mb-8 md:mb-0">
                <h2 class="mb-2 text-2xl font-semibold text-black">We're ready to give your SaaS the boost it needs!</h2>
                <p class="text-gray-600 sm:text-xl">You can contact us via email and our X account for a quick response. We usually reply within 24 hours.</p>
            </div>
            <div class="flex flex-col gap-4">
                <a 
                    href={encodeToBase64('mailto:<EMAIL>')}
                    class="btn-secondary btn-sm sm:flex shrink-0 whitespace-nowrap w-fit md:w-full justify-start"
                    on:mouseover={handleEmailInteraction}
                    on:focus={handleEmailInteraction}
                >
                    <svg class="w-4 h-4" fill="currentColor"><use href="#icon-email"></use></svg>
                    <span>Contact Us via Email</span>
                </a>
                <a target="_blank" href="https://x.com/submitsaas" class="btn-secondary btn-sm sm:flex shrink-0 whitespace-nowrap w-fit md:w-full justify-start">
                    <svg class="w-4 h-4" fill="currentColor"><use href="#icon-x"></use></svg>
                    <span>Contact Us on X</span>
                </a>
            </div>
        </div>
    </div>
</section>





