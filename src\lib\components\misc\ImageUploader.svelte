<script lang="ts">
    import { toast } from 'svelte-sonner';
    import type { UploadImageResponse } from '$lib/types/responses';

    // Define state variables
    let isUploading = $state(false);
    let fileInput: HTMLInputElement;

    let { value = $bindable(), placeholder, name = '', id = '', required = false, disabled = false, onChange } = $props();

    function handlePaste(event: ClipboardEvent): void {
        const items = event.clipboardData?.items;
        if (!items) return;

        for (let i = 0; i < items.length; i++) {
            if (items[i].type.indexOf('image') !== -1) {
                isUploading = true;
                const blob = items[i].getAsFile();
                if (blob) {
                    uploadImage(blob);
                }
                break;
            }
        }
    }

    // Handle file selection
    function handleFileSelect(event: Event): void {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];
        if (file && file.type.startsWith('image/')) {
            isUploading = true;
            uploadImage(file);
            // Reset file input
            if (fileInput) fileInput.value = '';
        }
    }

    // Upload image to server
    async function uploadImage(file: File): Promise<void> {
        try {
            console.log('Preparing to upload image:', {
                name: file.name,
                type: file.type,
                size: file.size
            });

            const formData = new FormData();
            formData.append('image', file);

            const response = await fetch('/api/UploadImage', {
                method: 'POST',
                body: formData,
                credentials: 'include'  // Include cookies and credentials
            });

            if (!response.ok) {
                toast.error('Failed to upload image');
                isUploading = false;
                return;
            }

            const result = await response.json() as UploadImageResponse;

            if (result.status === 'success') {
                console.log("Upload successful. Image URL:", result.image_url);
                value = result.image_url;
                onChange();
                toast.success('Image uploaded successfully');
            } else {
                console.error('Upload returned error:', result.message);
                toast.error(result.message || 'Failed to upload image');
            }
        } catch (error) {
            console.error('Error uploading image:', error);
            toast.error('Error uploading image: ' + (error instanceof Error ? error.message : String(error)));
        } finally {
            isUploading = false;
        }
    }

    function handleClick(): void {
        if (!disabled && fileInput) {
            fileInput.click();
        }
    }
</script>

<div class="relative flex items-center gap-2">
    <a
        aria-label="View Image"
        target="_blank"
        href={value || '#'}
        class="absolute left-0 top-[5px] p-2 {!value || isUploading ? 'pointer-events-none opacity-50' : ''}"
    >
        <svg class="h-3 w-3 fill-current">
            <use href="#icon-external"></use>
        </svg>
    </a>

    <input
        type="text"
        name={name}
        id={id}
        bind:value
        placeholder={placeholder}
        required={required}
        disabled={disabled || isUploading}
        onpaste={handlePaste}
        class="w-full rounded border border-gray-400 bg-white px-6 py-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none {isUploading ? 'opacity-70' : ''}"
        readonly
    />

    <button
        type="button"
        class="absolute right-0 top-[5px] p-2 {isUploading ? 'opacity-50' : ''}"
        onclick={handleClick}
        disabled={disabled || isUploading}
        aria-label="Upload image"
    >
        <svg class="h-3 w-3 fill-current">
            <use href="#icon-upload"></use>
        </svg>
    </button>

    <input
        type="file"
        accept="image/png, image/jpeg"
        onchange={handleFileSelect}
        bind:this={fileInput}
        style="display: none;"
    />
</div>
