<script lang="ts">
    import CustomDropdown from '$lib/components/misc/CustomDropdown.svelte';
    import CustomInputText from '$lib/components/misc/CustomInputText.svelte';
    import * as Dialog from '$lib/components/ui/dialog/index';
    import * as Popover from '$lib/components/ui/popover/index';
    import { toast } from 'svelte-sonner';
    import { onMount } from 'svelte';

    import type { SiteWithLabelIds } from '$lib/types/services';
    import type { Label } from '$lib/types/tables';
    import type { GetSitesRequest } from '$lib/types/requests';
    import type { CreateSiteResponse, DeleteSiteResponse, GetSitesResponse, UpdateSiteResponse } from '$lib/types/responses';
    import { labelExists, getLabelbyId, sortLabelsByOrder, sortLabelIdsByOrder } from '$lib/utils/labels';
    import { formatVisits } from '$lib/utils/format';

    // Props
    let { labels, labelsByGroup } = $props<{
        labels: Label[];
        labelsByGroup: Record<string, Label[]>;
    }>();

    interface Group {
        key: string;
        value: string;
    }

    const perPage = 20;

    let sites = $state<SiteWithLabelIds[]>([]);
    let isLoading = $state(true);
    let searchQuery = $state('');
    let searchTimeout: ReturnType<typeof setTimeout> | null = null;

    // Pagination state
    let currentPage = $state(1);
    let siteCount = $state(0);

    // Effect to load sites when labels are available
    $effect(() => {
        // If we have labels and we're still in the initial loading state, fetch sites
        if (labels.length > 0 && isLoading && sites.length === 0) {
            getSites().catch(() => {
                isLoading = false;
                toast.error('Failed to load sites');
            });
        }
    });

    function getTotalPages() {
        return Math.ceil(siteCount / perPage) || 1;
    }

    // State for the edit labels dialog
    let currentSiteIndex = $state<number | null>(null);
    let editLabelsDialogOpen = $state(false);

    let selectOptions = $state<Record<string, string>>({
        type: '',
        pricing: '',
        status: '',
        link_type: '',
        login: '',
        author_info: '',
        confirmation: '',
        form_display: '',
        difficulty: '',
        sort_by: 'dr'
    });

    let groups: Group[] = [
        { key: 'type', value: 'Type' },
        { key: 'pricing', value: 'Pricing' },
        { key: 'status', value: 'Status' },
        { key: 'link-type', value: 'Link Type' },
        { key: 'login', value: 'Login' },
        { key: 'author-info', value: 'Author Info' },
        { key: 'confirmation', value: 'Confirmation' },
        { key: 'form-display', value: 'Form Display' },
        { key: 'difficulty', value: 'Difficulty' },
    ];

    async function getSites(): Promise<void> {
        let selectedLabelIds = ['type', 'pricing', 'link_type', 'status', 'login', 'author_info', 'confirmation', 'form_display', 'difficulty']
            .filter(key => selectOptions[key])
            .map(key => selectOptions[key]);

        try {
            isLoading = true;

            const sitesRequest: GetSitesRequest = {
                andLabelIds: selectedLabelIds,
                page_num: currentPage,
                per_page: perPage,
                sort: selectOptions.sort_by,
                search: searchQuery && searchQuery.length >= 2 ? searchQuery : ''
            };

            const response = await fetch(`/api/GetSites`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(sitesRequest)
            });
            const data = await response.json() as GetSitesResponse;

            sites = data.sites;
            siteCount = data.site_count;
        } catch (error) {
            console.error('Error fetching sites:', error);
            toast.error('Failed to fetch sites');
        } finally {
            isLoading = false;
        }
    };

    // Function to handle page changes
    function handlePageChange(page: number) {
        const totalPages = getTotalPages();
        if (page < 1 || page > totalPages) return;
        currentPage = page;
        getSites();
    }

    function handleSearch(event: Event): void {
        const input = event.target as HTMLInputElement;
        searchQuery = input.value;

        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        if (searchQuery.length >= 2 || searchQuery.length === 0) {
            searchTimeout = setTimeout(() => {
                // Reset to first page when searching
                currentPage = 1;
                getSites();
            }, 500);
        }
    }

    function adjustHeight(event: Event): void {
        const textarea = event.target as HTMLTextAreaElement;
        textarea.style.height = 'auto';
        textarea.style.height = `${textarea.scrollHeight + 2}px`;
    }

    function restoreHeight(event: Event): void {
        const textarea = event.target as HTMLTextAreaElement;
        textarea.style.removeProperty('height');
    }

    function openEditLabelsDialog(index: number): void {
        currentSiteIndex = index;
        editLabelsDialogOpen = true;
    }

    async function deleteSite(site: SiteWithLabelIds): Promise<void> {
        if (!confirm(`Are you sure you want to delete the site for ${site.site_name || 'this site'}?`)) {
            return;
        }
        if (site.site_id === '0') {
            sites = sites.filter((s) => s.site_id !== site.site_id);
            return;
        }

        try {
            const response = await fetch(`/api/DeleteSite`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ site_id: site.site_id })
            });
            const data = await response.json() as DeleteSiteResponse;
            if (data.status === 'success') {
                sites = sites.filter((s) => s.site_id !== site.site_id);
                toast.success(data.message);
            } else {
                toast.error(data.message);
            }
        } catch (error) {
            toast.error('Failed to delete site');
            console.error('Error deleting site:', error);
        }
    }

    async function saveSite(index: number): Promise<void> {
        const site = sites[index];
        try {
            if (site.site_id === '0') {
                const response = await fetch(`/api/CreateSite`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(site),
                });
                const data = await response.json() as CreateSiteResponse;
                if (data.status === 'success') {
                    site.site_id = data.site.site_id;
                    site.site_dr = data.site.site_dr;
                    site.site_visits = data.site.site_visits;
                    toast.success(data.message);
                } else {
                    toast.error(data.message);
                }
            } else {
                const response = await fetch(`/api/UpdateSite`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(site),
                });
                const data = await response.json() as UpdateSiteResponse;
                if (data.status === 'success') {
                    toast.success(data.message);
                } else {
                    toast.error(data.message);
                }
            }
        } catch (error) {
            toast.error('Failed to save site');
            console.error('Error saving site:', error);
        }
    }

    onMount(() => {
        // The sites will be loaded via the effect when labels are available
        // This prevents duplicate loading and ensures labels are available first
        isLoading = true;
    });
</script>

<div class="mb-12 flex items-center justify-between">
    <h2 class="text-3xl font-bold tracking-tight text-white md:text-4xl">Sites</h2>
    <button
        onclick={() => {
            sites = [
                {
                    site_id: '0',
                    site_name: '',
                    site_url: '',
                    site_submit_url: '',
                    site_actual_submit_url: '',
                    site_note: '',
                    site_waiting_time: '',
                    site_created_at: Math.floor(Date.now() / 1000),
                    site_dr: 0.0,
                    site_visits: 0,
                    label_ids: [],
                },
                ...sites,
            ];
        }}
        class="btn-primary w-fit"
    >
        <svg class="h-4 w-4 fill-white">
            <use href="#icon-plus"></use>
        </svg>
        New
    </button>
</div>

<!-- Table -->
<div class="relative overflow-hidden rounded-lg bg-white ring-4 ring-white/25">
    <!-- Search Input -->
    <div class="border-b border-gray-300 p-4">
        <CustomInputText
            name="site-search"
            title="Search"
            type="search"
            placeholder="Search sites..."
            value={searchQuery}
            onInput={handleSearch}
            error={""}
        />
    </div>

    <div
        class="flex flex-col items-start justify-between gap-4 border-b border-gray-300 p-4 lg:flex-row lg:items-center"
    >
        <div class="grid w-full gap-4 sm:grid-cols-2 md:grid-cols-4">
            <CustomDropdown
                onChange={() => {
                    currentPage = 1;
                    getSites();
                }}
                bind:value={selectOptions.type}
                labels={labelsByGroup['type']}
                placeholder="All"
                title="Type:"
            />

            <CustomDropdown
                onChange={() => {
                    currentPage = 1;
                    getSites();
                }}
                bind:value={selectOptions.pricing}
                labels={labelsByGroup['pricing']}
                placeholder="All"
                title="Pricing:"
            />

            <CustomDropdown
                onChange={() => {
                    currentPage = 1;
                    getSites();
                }}
                bind:value={selectOptions.status}
                labels={labelsByGroup['status']}
                placeholder="All"
                title="Status:"
            />

            <CustomDropdown
                onChange={() => {
                    currentPage = 1;
                    getSites();
                }}
                bind:value={selectOptions.link_type}
                labels={labelsByGroup['link-type']}
                placeholder="All"
                title="Link Type:"
            />

            <CustomDropdown
                onChange={() => {
                    currentPage = 1;
                    getSites();
                }}
                bind:value={selectOptions.login}
                labels={labelsByGroup['login']}
                placeholder="All"
                title="Login Type:"
            />

            <CustomDropdown
                onChange={() => {
                    currentPage = 1;
                    getSites();
                }}
                bind:value={selectOptions.author_info}
                labels={labelsByGroup['author-info']}
                placeholder="All"
                title="Author Info:"
            />

            <CustomDropdown
                onChange={() => {
                    currentPage = 1;
                    getSites();
                }}
                bind:value={selectOptions.confirmation}
                labels={labelsByGroup['confirmation']}
                placeholder="All"
                title="Confirmation:"
            />

            <CustomDropdown
                onChange={() => {
                    currentPage = 1;
                    getSites();
                }}
                bind:value={selectOptions.form_display}
                labels={labelsByGroup['form-display']}
                placeholder="All"
                title="Form Display:"
            />

            <CustomDropdown
                onChange={() => {
                    currentPage = 1;
                    getSites();
                }}
                bind:value={selectOptions.difficulty}
                labels={labelsByGroup['difficulty']}
                placeholder="All"
                title="Difficulty:"
            />

            <CustomDropdown
                onChange={() => {
                    currentPage = 1;
                    getSites();
                }}
                bind:value={selectOptions.sort_by}
                labels={[
                    { label_id: 'dr', label_name: 'DR' },
                    { label_id: 'visits', label_name: 'Visits' },
                ]}
                placeholder=""
                title="Sort by:"
            />
        </div>
    </div>
    <div class="overflow-x-auto">
        <div class="overflow-auto">
            <table class="w-full table-fixed text-left text-sm font-semibold text-gray-500">
                <thead class="bg-white text-xs uppercase text-gray-600">
                    <tr class="border-b border-gray-300">
                        <th scope="col" class="w-[60px] whitespace-nowrap py-4 pl-4 pr-2">#</th>
                        <th scope="col" class="w-[90px] whitespace-nowrap px-2 py-4">Actions</th>
                        <th scope="col" class="w-[160px] whitespace-nowrap px-2 py-4">Name</th>
                        <th scope="col" class="w-[140px] whitespace-nowrap px-2 py-4">URL</th>
                        <th scope="col" class="w-[140px] whitespace-nowrap px-2 py-4">Submit URL</th>
                        <th scope="col" class="w-[140px] whitespace-nowrap px-2 py-4">Actual Submit URL</th>
                        <th scope="col" class="w-[300px] whitespace-nowrap px-2 py-4">Labels</th>
                        <th scope="col" class="w-[200px] whitespace-nowrap px-2 py-4">Waiting Time</th>
                        <th scope="col" class="w-[200px] whitespace-nowrap px-2 py-4">Note</th>
                        <th scope="col" class="w-[50px] whitespace-nowrap px-2 py-4">DR</th>
                        <th scope="col" class="w-[80px] whitespace-nowrap py-4 pl-2 pr-4">Visits</th>
                    </tr>
                </thead>
                {#if isLoading}
                    <tbody class="h-[860px] text-sm text-black">
                        <tr>
                            <td colspan="11" class="p-4 text-center">
                                <div class="flex items-center justify-center gap-2">
                                    <div
                                        class="h-8 w-8 animate-spin rounded-full border-2 border-gray-200 border-t-gray-400"
                                    ></div>
                                    <span class="font-semibold">Loading...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                {:else}
                    <tbody class="text-sm text-black">
                        {#each sites as site, index}
                            <tr class="border-b border-gray-300 hover:bg-gray-200">
                                <td class="py-2 pl-4 pr-2">
                                    {((currentPage - 1) * perPage) + index + 1}.
                                </td>
                                <td class="px-2 py-2">
                                    <div class="flex items-center gap-1">
                                        <button
                                            onclick={() => {
                                                saveSite(index);
                                            }}
                                            data-index={index}
                                            data-id={site.site_id}
                                            class="rounded-full border border-gray-400 bg-white p-2"
                                            aria-label="Edit"
                                        >
                                            <svg class="h-4 w-4 stroke-current">
                                                <use href="#icon-save"></use>
                                            </svg>
                                        </button>
                                        <button
                                            onclick={() => {
                                                deleteSite(site);
                                            }}
                                            data-index={index}
                                            data-id={site.site_id}
                                            class="rounded-full border border-gray-400 bg-white p-2"
                                            aria-label="Delete"
                                        >
                                            <svg class="h-4 w-4 stroke-current">
                                                <use href="#icon-delete"></use>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap px-2 py-2">
                                    <input
                                        class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                                        type="text"
                                        bind:value={site.site_name}
                                    />
                                </td>
                                <td class="whitespace-nowrap px-2 py-2">
                                    <div class="relative flex items-center gap-2">
                                        <a
                                            aria-label="Visit Site URL"
                                            href={site.site_url}
                                            target="_blank"
                                            class="{site.site_url == ''
                                                ? 'pointer-events-none opacity-50'
                                                : ''} absolute left-0 top-[5px] p-2"
                                        >
                                            <svg class="h-3 w-3 fill-current">
                                                <use href="#icon-external"></use>
                                            </svg>
                                        </a>
                                        <input
                                            class="w-full rounded border border-gray-400 bg-white p-2 pl-6 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                                            type="text"
                                            bind:value={site.site_url}
                                        />
                                    </div>
                                </td>
                                <td class="whitespace-nowrap px-2 py-2">
                                    <div class="relative flex items-center gap-2">
                                        <a
                                            aria-label="Visit Submit URL"
                                            href={site.site_submit_url}
                                            target="_blank"
                                            class="{site.site_submit_url == ''
                                                ? 'pointer-events-none opacity-50'
                                                : ''} absolute left-0 top-[5px] p-2"
                                        >
                                            <svg class="h-3 w-3 fill-current">
                                                <use href="#icon-external"></use>
                                            </svg>
                                        </a>
                                        <input
                                            class="w-full rounded border border-gray-400 bg-white p-2 pl-6 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                                            type="text"
                                            bind:value={site.site_submit_url}
                                        />
                                    </div>
                                </td>
                                <td class="whitespace-nowrap px-2 py-2">
                                    <div class="relative flex items-center gap-2">
                                        <a
                                            aria-label="Visit Actual Submit URL"
                                            href={site.site_actual_submit_url}
                                            target="_blank"
                                            class="{site.site_actual_submit_url == ''
                                                ? 'pointer-events-none opacity-50'
                                                : ''} absolute left-0 top-[5px] p-2"
                                        >
                                            <svg class="h-3 w-3 fill-current">
                                                <use href="#icon-external"></use>
                                            </svg>
                                        </a>
                                        <input
                                            class="w-full rounded border border-gray-400 bg-white p-2 pl-6 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                                            type="text"
                                            bind:value={site.site_actual_submit_url}
                                        />
                                    </div>
                                </td>

                                <td class="px-2 py-2">
                                    <div class="relative flex items-center gap-1">
                                        <button
                                            onclick={() => openEditLabelsDialog(index)}
                                            class="left-0 top-0 rounded-full border border-gray-400 bg-white p-2"
                                            aria-label="Edit Labels"
                                        >
                                            <svg class="h-4 w-4 fill-black">
                                                <use href="#icon-edit"></use>
                                            </svg>
                                        </button>

                                        <Popover.Root>
                                            <Popover.Trigger
                                                class="left-0 top-0 rounded-full border border-gray-400 bg-white p-2"
                                            >
                                                <svg class="h-4 w-4 fill-black">
                                                    <use href="#icon-caret-down"></use>
                                                </svg>
                                            </Popover.Trigger>
                                            <Popover.Content>
                                                <div class="flex flex-wrap gap-1">
                                                    {#each sortLabelIdsByOrder(site.label_ids, labels) as label_id}
                                                        {#if labelExists(label_id, labels)}
                                                            <div
                                                                style="background:{getLabelbyId(label_id, labels)?.label_color}1A;border-color:{getLabelbyId(label_id, labels)?.label_color};color:{getLabelbyId(label_id, labels)?.label_color}"
                                                                class="w-fit whitespace-nowrap rounded-2xl border border-gray-400 bg-white px-2 py-1 text-xs leading-none"
                                                            >
                                                                {getLabelbyId(label_id, labels)?.label_name}
                                                            </div>
                                                        {/if}
                                                    {/each}
                                                </div>
                                            </Popover.Content>
                                        </Popover.Root>

                                        <div class="flex w-full flex-1 gap-1 overflow-hidden">
                                            {#each sortLabelIdsByOrder(site.label_ids, labels) as label_id}
                                                {#if getLabelbyId(label_id, labels)}
                                                    <div
                                                        style="background:{getLabelbyId(label_id, labels)?.label_color}1A;border-color:{getLabelbyId(label_id, labels)?.label_color};color:{getLabelbyId(label_id, labels)?.label_color}"
                                                        class="w-fit whitespace-nowrap rounded-2xl border border-gray-400 bg-white px-2 py-1 text-xs leading-none"
                                                    >
                                                        {getLabelbyId(label_id, labels)?.label_name}
                                                    </div>
                                                {/if}
                                            {/each}
                                        </div>
                                    </div>
                                </td>

                                <td class="px-2 py-2">
                                    <div class="relative h-[38px] w-full">
                                        <textarea
                                            bind:value={site.site_waiting_time}
                                            onfocusin={adjustHeight}
                                            onfocusout={restoreHeight}
                                            oninput={adjustHeight}
                                            class="absolute left-0 top-0 block h-full w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:z-10 focus:border-gray-600 focus:outline-none"
                                            placeholder="Waiting Time"
                                        >
                                        </textarea>
                                    </div>
                                </td>

                                <td class="px-2 py-2">
                                    <div class="relative h-[38px] w-full">
                                        <textarea
                                            bind:value={site.site_note}
                                            onfocusin={adjustHeight}
                                            onfocusout={restoreHeight}
                                            oninput={adjustHeight}
                                            class="absolute left-0 top-0 block h-full w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:z-10 focus:border-gray-600 focus:outline-none"
                                            placeholder="Note"
                                        >
                                        </textarea>
                                    </div>
                                </td>

                                <td class="px-2 py-2">
                                    {site.site_dr}
                                </td>
                                <td class="py-2 pl-2 pr-4">
                                    {formatVisits(site.site_visits)}
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                {/if}
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {#if !isLoading && sites.length > 0}
        <nav
            class="mt-[-1px] flex flex-col items-start justify-between space-y-3 border-t border-gray-300 bg-white p-4 md:flex-row md:items-center md:space-y-0"
            aria-label="Table navigation"
        >
            <span class="text-xs font-normal text-black">
                Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, siteCount)} of {siteCount} sites
            </span>
            <ul class="flex items-center -space-x-px">
                <li>
                    <span class="pr-4 text-xs">
                        Page {currentPage} of {getTotalPages()}
                    </span>
                </li>
                <li>
                    <button
                        class="ml-0 flex h-full w-auto items-center justify-center rounded-l-lg border border-gray-400 bg-white px-4 py-2 text-sm font-semibold text-black hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
                        onclick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        Previous
                    </button>
                </li>
                <li>
                    <button
                        class="ml-0 flex h-full w-auto items-center justify-center rounded-r-lg border border-gray-400 bg-white px-4 py-2 text-sm font-semibold text-black hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50"
                        onclick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage >= getTotalPages()}
                    >
                        Next
                    </button>
                </li>
            </ul>
        </nav>
    {/if}
</div>
<!-- /Table -->

<!-- Shared Edit Labels Dialog -->
<Dialog.Root bind:open={editLabelsDialogOpen}>
    <Dialog.Content>
        <Dialog.Header>
            <h3 class="mb-4 text-xl font-semibold">Edit Labels</h3>
        </Dialog.Header>
        {#if currentSiteIndex !== null && sites[currentSiteIndex]}
            <div class="flex flex-col gap-4">
                {#each groups as group}
                    <div class="flex flex-col gap-2">
                        <h4 class="mb-1 border-b border-gray-400 pb-1 text-sm font-semibold uppercase">
                            {group.value}
                        </h4>
                        <div class="grid grid-cols-2 gap-2">
                            {#each sortLabelsByOrder(labels.filter((label: Label) => label.label_group === group.key)) as label}
                                <label class="flex gap-2">
                                    <input
                                        name="label-{group.key}"
                                        class="h-5 w-5"
                                        type="checkbox"
                                        value={label.label_id}
                                        bind:group={sites[currentSiteIndex].label_ids}
                                    />
                                    <span class="text-sm leading-tight">
                                        {label.label_name}
                                    </span>
                                </label>
                            {/each}
                        </div>
                    </div>
                {/each}
            </div>
            <Dialog.Footer class="mt-4">
                <Dialog.Close class="btn-primary btn-sm w-fit">
                    Save changes
                </Dialog.Close>
            </Dialog.Footer>
        {/if}
    </Dialog.Content>
</Dialog.Root>
