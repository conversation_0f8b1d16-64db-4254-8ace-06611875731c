import { json } from '@sveltejs/kit';
import { getNotes } from '$lib/services/notes';
import type { <PERSON>questH<PERSON><PERSON> } from './$types';
import type { GetNotesResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const notes = await getNotes();
    
    return json({
      status: 'success',
      message: 'Notes fetched successfully',
      notes
    } as GetNotesResponse, { status: 200 });
  } catch (error) {
    console.error('Unexpected error at GetNotes:', error);
    return json({
      status: 'error',
      message: `Unexpected error at GetNotes: ${error instanceof Error ? error.message : 'Unknown error'}`,
      notes: []
    } as GetNotesResponse, { status: 500 });
  }
}
