export function generatePassword(length: number): string {
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    const password: string[] = [];

    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * charset.length);
        password.push(charset[randomIndex]);
    }

    return password.join('');
}

export function generateReportPassword(): string {
    return generatePassword(12);
}