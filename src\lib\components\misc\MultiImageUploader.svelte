<script lang="ts">
    import { toast } from 'svelte-sonner';
    import type { UploadImageResponse } from '$lib/types/responses';

    let isUploading = $state(false);
    let fileInput: HTMLInputElement;

    let { value = $bindable([]), onChange } = $props<{
        value: string[];
        onChange: () => void;
    }>();

    function handleFileSelect(event: Event): void {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];
        if (file && file.type.startsWith('image/')) {
            isUploading = true;
            uploadImage(file);
            if (fileInput) fileInput.value = '';
        }
    }

    async function uploadImage(file: File): Promise<void> {
        try {
            console.log('Preparing to upload image:', {
                name: file.name,
                type: file.type,
                size: file.size
            });

            const formData = new FormData();
            formData.append('image', file);

            const response = await fetch('/api/UploadImage', {
                method: 'POST',
                body: formData,
                credentials: 'include'
            });

            if (!response.ok) {
                toast.error('Failed to upload image');
                isUploading = false;
                return;
            }

            const result = await response.json() as UploadImageResponse;

            if (result.status === 'success') {
                console.log("Upload successful. Image URL:", result.image_url);
                value = [...value, result.image_url];
                onChange();
                toast.success('Image uploaded successfully');
            } else {
                console.error('Upload returned error:', result.message);
                toast.error(result.message || 'Failed to upload image');
            }
        } catch (error) {
            console.error('Error uploading image:', error);
            toast.error('Error uploading image: ' + (error instanceof Error ? error.message : String(error)));
        } finally {
            isUploading = false;
        }
    }

    function handleClick(): void {
        if (!isUploading && fileInput) {
            fileInput.click();
        }
    }

    function removeImage(index: number): void {
        value = value.filter((_: string, i: number) => i !== index);
        onChange();
    }
</script>

<div class="flex items-center gap-1 flex-wrap">
    <!-- Upload button -->
    <button
        type="button"
        class="flex h-10 w-10 items-center justify-center rounded border border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100 {isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}"
        onclick={handleClick}
        disabled={isUploading}
        aria-label="Upload image"
    >
        {#if isUploading}
            <div class="h-4 w-4 animate-spin rounded-full border border-gray-300 border-t-gray-600"></div>
        {:else}
            <svg class="h-4 w-4 text-gray-400 stroke-current">
                <use href="#icon-plus"></use>
            </svg>
        {/if}
    </button>

    <!-- Display uploaded images -->
    {#each value as imageUrl, index}
        <div class="relative group">
            <img
                src={imageUrl}
                alt="Screenshot {index + 1}"
                class="h-10 w-10 object-cover rounded border border-gray-300"
            />
            <button
                type="button"
                class="absolute left-1/2 top-1/2 h-6 w-6 rounded-full bg-red-500 text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center transform -translate-x-1/2 -translate-y-1/2"
                onclick={() => removeImage(index)}
                aria-label="Remove image"
            >
                <svg class="h-4 w-4 fill-white">
                    <use href="#icon-xmark"></use>
                </svg>
            </button>
        </div>
    {/each}

    <!-- Hidden file input -->
    <input
        type="file"
        accept="image/png, image/jpeg, image/gif"
        onchange={handleFileSelect}
        bind:this={fileInput}
        style="display: none;"
    />
</div>
