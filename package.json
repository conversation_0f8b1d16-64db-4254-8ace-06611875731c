{"name": "submitsaas", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@lucide/svelte": "^0.482.0", "@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.13.10", "autoprefixer": "^10.4.20", "bits-ui": "^1.4.8", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^2.46.1", "globals": "^15.14.0", "mode-watcher": "^0.5.1", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.10", "svelte": "^5.0.0", "svelte-adapter-bun": "^0.5.2", "svelte-check": "^4.0.0", "svelte-sonner": "^0.3.28", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.0.0"}, "dependencies": {"@types/jsonwebtoken": "^9.0.9", "exceljs": "^4.4.0", "jsonwebtoken": "^9.0.2", "postgres": "^3.4.5", "tailwind-merge": "^3.0.2", "uuid": "^11.1.0"}}