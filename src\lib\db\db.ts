import postgres from 'postgres';
import type { Options } from 'postgres';
import { isProd } from '$lib/utils/env';

const connectionConfig: Options<{}> = isProd
  ? {
      host: '/var/run/postgresql',
      database: 'submitsaas',
      username: 'demo',
      password: 'soydemo.com',
      ssl: false,
    }
  : {
      host: '*************',
      port: 5432,
      database: 'submitsaas',
      username: 'demo',
      password: 'soydemo.com',
      ssl: false,
    };

const sql = postgres(connectionConfig);

export default sql;