import sql from '$lib/db/db';
import type { SiteWithLabelIds } from '$lib/types/services';
import type { Site } from '$lib/types/tables';
import { generateUUID } from '$lib/utils/uuid';

export async function getSites(
    andLabelIds?: string[],
    orLabelIds?: string[],
    pageNum?: number,
    perPage?: number,
    sort?: string,
    searchQuery?: string
): Promise<{ sites: SiteWithLabelIds[]; site_count: number, page_num: number, per_page: number }> {

    andLabelIds = andLabelIds || [];
    orLabelIds = orLabelIds || [];
    pageNum = pageNum || 1;
    perPage = perPage || 20;
    sort = sort || 'dr';
    searchQuery = searchQuery || '';

    const validPageNum = pageNum <= 0 ? 1 : pageNum;
    const offset = perPage > 0 ? (validPageNum - 1) * perPage : 0;
    const orderBy = sort === 'visits' ? 'site_visits DESC' : 'site_dr DESC';

    let countQueryParts = [`
        SELECT COUNT(*) AS total_count
        FROM sites s
    `];

    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (searchQuery) {
        conditions.push(`(s.site_name ILIKE $${paramIndex} OR s.site_url ILIKE $${paramIndex} OR s.site_submit_url ILIKE $${paramIndex})`);
        params.push(`%${searchQuery}%`);
        paramIndex++;
    }

    // Handle AND filtering (sites must have ALL of these labels)
    if (andLabelIds.length > 0) {
        const labelIdsStr = andLabelIds.map(id => `'${id}'`).join(',');
        conditions.push(`
            s.site_id IN (
                SELECT site_id
                FROM site_label_rels
                WHERE label_id IN (${labelIdsStr})
                GROUP BY site_id
                HAVING COUNT(DISTINCT label_id) = ${andLabelIds.length}
            )
        `);
    }

    // Handle OR filtering (sites must have AT LEAST ONE of these labels)
    if (orLabelIds.length > 0) {
        const labelIdsStr = orLabelIds.map(id => `'${id}'`).join(',');
        conditions.push(`
            s.site_id IN (
                SELECT site_id
                FROM site_label_rels
                WHERE label_id IN (${labelIdsStr})
            )
        `);
    }

    if (conditions.length > 0) {
        countQueryParts.push('WHERE ' + conditions.join(' AND '));
    }

    const countResult = await sql.unsafe(countQueryParts.join(' '), params);
    const total = parseInt(countResult[0].total_count);

    let queryParts = [`
        SELECT
            s.site_id, s.site_name, s.site_url, s.site_submit_url, s.site_actual_submit_url, s.site_note,
            s.site_waiting_time, s.site_created_at, s.site_dr, s.site_dr_updated_at,
            s.site_visits, s.site_visits_updated_at,
            COALESCE(
                ARRAY_AGG(l.label_id) FILTER (WHERE l.label_id IS NOT NULL),
                '{}'::uuid[]
            ) AS label_ids
        FROM sites s
        LEFT JOIN site_label_rels slr ON s.site_id = slr.site_id
        LEFT JOIN labels l ON slr.label_id = l.label_id
    `];

    if (conditions.length > 0) {
        queryParts.push('WHERE ' + conditions.join(' AND '));
    }

    queryParts.push(`
        GROUP BY s.site_id, s.site_name, s.site_url, s.site_submit_url, s.site_actual_submit_url, s.site_note,
            s.site_waiting_time, s.site_created_at, s.site_dr, s.site_dr_updated_at,
            s.site_visits, s.site_visits_updated_at
    `);

    queryParts.push(`ORDER BY ${orderBy}`);

    if (perPage > 0) {
        queryParts.push(`LIMIT ${perPage} OFFSET ${offset}`);
    }

    const sites = await sql.unsafe<SiteWithLabelIds[]>(queryParts.join(' '), params);

    return {
        sites,
        site_count: total,
        page_num: validPageNum,
        per_page: perPage > 0 ? perPage : total
    };
}

export async function getSitesWithFormFields(
    andLabelIds?: string[],
    orLabelIds?: string[],
    searchQuery?: string,
    fieldType?: string,
    matchReportField?: string
): Promise<{ sites: SiteWithLabelIds[] }> {

    andLabelIds = andLabelIds || [];
    orLabelIds = orLabelIds || [];
    searchQuery = searchQuery || '';
    fieldType = fieldType || '';
    matchReportField = matchReportField || '';

    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (searchQuery) {
        conditions.push(`(s.site_name ILIKE $${paramIndex} OR s.site_url ILIKE $${paramIndex} OR s.site_submit_url ILIKE $${paramIndex})`);
        params.push(`%${searchQuery}%`);
        paramIndex++;
    }

    if (andLabelIds.length > 0) {
        const labelIdsStr = andLabelIds.map(id => `'${id}'`).join(',');
        conditions.push(`
            s.site_id IN (
                SELECT site_id
                FROM site_label_rels
                WHERE label_id IN (${labelIdsStr})
                GROUP BY site_id
                HAVING COUNT(DISTINCT label_id) = ${andLabelIds.length}
            )
        `);
    }

    if (orLabelIds.length > 0) {
        const labelIdsStr = orLabelIds.map(id => `'${id}'`).join(',');
        conditions.push(`
            s.site_id IN (
                SELECT site_id
                FROM site_label_rels
                WHERE label_id IN (${labelIdsStr})
            )
        `);
    }

    // Add form field filtering using simple text search
    if (fieldType) {
        // Escape single quotes in fieldType to prevent SQL injection
        const escapedFieldType = fieldType.replace(/'/g, "''");
        conditions.push(`
            s.site_form_fields IS NOT NULL
            AND s.site_form_fields::text LIKE '%${escapedFieldType}%'
        `);
    }

    if (matchReportField) {
        // Escape single quotes in matchReportField to prevent SQL injection
        const escapedMatchField = matchReportField.replace(/'/g, "''");
        conditions.push(`
            s.site_form_fields IS NOT NULL
            AND s.site_form_fields::text LIKE '%${escapedMatchField}%'
        `);
    }

    const sites = await sql.unsafe<SiteWithLabelIds[]>(`
        SELECT
            s.site_id, s.site_name, s.site_url, s.site_submit_url, s.site_actual_submit_url, s.site_note,
            s.site_waiting_time, s.site_created_at, s.site_dr, s.site_dr_updated_at,
            s.site_visits, s.site_visits_updated_at, s.site_form_fields,
            COALESCE(
                ARRAY_AGG(l.label_id) FILTER (WHERE l.label_id IS NOT NULL),
                '{}'::uuid[]
            ) AS label_ids
        FROM sites s
        LEFT JOIN site_label_rels slr ON s.site_id = slr.site_id
        LEFT JOIN labels l ON slr.label_id = l.label_id
        ${conditions.length > 0 ? 'WHERE ' + conditions.join(' AND ') : ''}
        GROUP BY s.site_id, s.site_name, s.site_url, s.site_submit_url, s.site_actual_submit_url, s.site_note,
            s.site_waiting_time, s.site_created_at, s.site_dr, s.site_dr_updated_at,
            s.site_visits, s.site_visits_updated_at
        ORDER BY s.site_dr DESC
    `, params);

    return {
        sites
    };
}

export async function siteExists(siteUrl: string): Promise<boolean> {
    const result = await sql`
        SELECT EXISTS(
            SELECT 1
            FROM sites
            WHERE site_url ILIKE ${siteUrl}
            LIMIT 1
        )
    `;

    return result[0].exists;
}

export async function createSite(site: Partial<Site>): Promise<string> {
    const currentTime = Math.floor(Date.now() / 1000);
    const uuid = generateUUID();

    const result = await sql`
        INSERT INTO sites (
            site_id,
            site_name,
            site_url,
            site_submit_url,
            site_actual_submit_url,
            site_waiting_time,
            site_note,
            site_created_at
        ) VALUES (
            ${uuid},
            ${site.site_name || ''},
            ${site.site_url || ''},
            ${site.site_submit_url || ''},
            ${site.site_actual_submit_url || ''},
            ${site.site_waiting_time || ''},
            ${site.site_note || ''},
            ${currentTime}
        )
        RETURNING site_id
    `;

    return result[0].site_id;
}

export async function updateSite(
    siteId: string,
    site: {
        site_name: string;
        site_url: string;
        site_submit_url: string;
        site_actual_submit_url: string;
        site_waiting_time: string;
        site_note: string;
    }
): Promise<string> {
    const result = await sql`
        UPDATE sites
        SET
            site_name = ${site.site_name},
            site_url = ${site.site_url},
            site_submit_url = ${site.site_submit_url},
            site_actual_submit_url = ${site.site_actual_submit_url},
            site_waiting_time = ${site.site_waiting_time},
            site_note = ${site.site_note}
        WHERE site_id = ${siteId}
        RETURNING site_id
    `;

    if (result.length === 0) {
        throw new Error('No site found with the given ID');
    }

    return result[0].site_id;
}

export async function deleteSite(siteId: string): Promise<void> {
    const result = await sql`
        DELETE FROM sites
        WHERE site_id = ${siteId}
        RETURNING site_id
    `;

    if (result.length === 0) {
        throw new Error('No site found with the given ID');
    }
}

export async function saveSiteJSON(
    site_id: string,
    json_data: string
): Promise<void> {
    const result = await sql`
        UPDATE sites
        SET site_form_fields = ${json_data}
        WHERE site_id = ${site_id}
        RETURNING site_id
    `;

    if (result.length === 0) {
        throw new Error('No site found with the given ID');
    }
}

export async function getSiteJSON(url: string): Promise<{ site_id: string; site_url: string; json_data: string | null } | null> {
    const result = await sql<{ site_id: string; site_url: string; site_form_fields: string | null }[]>`
        SELECT site_id, site_url, site_form_fields
        FROM sites
        WHERE
            (
                (site_url ILIKE ${url} || '%' OR ${url} ILIKE site_url || '%')
                AND site_url != ''
                AND site_url IS NOT NULL
            )
            OR
            (
                (site_submit_url ILIKE ${url} || '%' OR ${url} ILIKE site_submit_url || '%')
                AND site_submit_url != ''
                AND site_submit_url IS NOT NULL
            )
            OR
            (
                (site_actual_submit_url ILIKE ${url} || '%' OR ${url} ILIKE site_actual_submit_url || '%')
                AND site_actual_submit_url != ''
                AND site_actual_submit_url IS NOT NULL
            )
        ORDER BY LENGTH(site_submit_url) DESC
        LIMIT 1
    `;

    if (result.length === 0) {
        return null;
    }

    return {
      site_id: result[0].site_id,
      site_url: result[0].site_url,
      json_data: result[0].site_form_fields
    };
}
