import { json } from '@sveltejs/kit';
import type { <PERSON>questHand<PERSON> } from './$types';
import { getReport } from '$lib/services/reports';
import { getLabels } from '$lib/services/labels';
import { getReportSites } from '$lib/services/report_sites';
import { getLabelbyId } from '$lib/utils/labels';
import { formatVisits } from '$lib/utils/format';
import type { Label } from '$lib/types/tables';
import type { ReportSiteWithLabels } from '$lib/types/services';
import ExcelJS from 'exceljs';

const headerStyle: Partial<ExcelJS.Style> = {
    font: {
        color: { argb: 'FF0000FF' },
        bold: true
    },
    fill: {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFFFFF00' }
    } as ExcelJS.FillPattern
};

const hyperlinkStyle: Partial<ExcelJS.Style> = {
    font: {
        color: { argb: 'FF0000FF' },
        underline: true
    }
};

function createSheet(): ExcelJS.Workbook {
    const workbook = new ExcelJS.Workbook();
    workbook.addWorksheet('Sheet1');
    return workbook;
}

function addInfo(workbook: ExcelJS.Workbook, websiteUrl: string, email: string, password: string): void {
    const sheet = workbook.getWorksheet('Sheet1');
    if (!sheet) return;

    sheet.getCell('A1').value = 'WEBSITE URL';
    sheet.getCell('A1').style = headerStyle;
    sheet.getCell('B1').value = websiteUrl;

    sheet.getCell('A2').value = 'EMAIL CREDENTIALS';
    sheet.getCell('A2').style = headerStyle;
    sheet.getCell('B2').value = email;
    sheet.getCell('C2').value = password;
}

function addHeaders(workbook: ExcelJS.Workbook): void {
    const sheet = workbook.getWorksheet('Sheet1');
    if (!sheet) return;

    const headers = [
        'NAME', 'TYPE', 'DR', 'VISITS', 'SITE URL', 'SUBMIT URL', 'SCREENSHOT 1', 'SCREENSHOT 2', 'SCREENSHOT 3', 'SCREENSHOT 4'
    ];

    // Set custom column widths
    const columnWidths = [
        200 / 7,    // NAME - normal width
        200 / 7,    // TYPE - normal width
        (200 / 7) * 0.5,  // DR - 50% shorter
        (200 / 7) * 0.5,  // VISITS - 50% shorter
        200 / 7,    // SITE URL - normal width
        200 / 7,    // SUBMIT URL - normal width
        (200 / 7) * 0.7,  // SCREENSHOT 1 - reduced width
        (200 / 7) * 0.7,  // SCREENSHOT 2 - reduced width
        (200 / 7) * 0.7,  // SCREENSHOT 3 - reduced width
        (200 / 7) * 0.7   // SCREENSHOT 4 - reduced width
    ];

    columnWidths.forEach((width, index) => {
        const col = sheet.getColumn(index + 1);
        col.width = width;
    });

    headers.forEach((header, index) => {
        const cell = sheet.getCell(4, index + 1);
        cell.value = header;
        cell.style = headerStyle;
    });
}

function addRow(workbook: ExcelJS.Workbook, row: ReportSiteWithLabels, rowIndex: number, labels: Label[]): void {
    const sheet = workbook.getWorksheet('Sheet1');
    if (!sheet) return;

    let siteType = '';
    if (row.label_ids) {
        for (const labelId of row.label_ids) {
            const label = getLabelbyId(labelId, labels);
            if (label && label.label_group === 'type') {
                siteType = label.label_name;
                break;
            }
        }
    }

    // Prepare screenshot URLs with padding to ensure we have 4 slots
    const screenshotUrls = [...row.screenshot_urls];
    while (screenshotUrls.length < 4) {
        screenshotUrls.push('');
    }

    const rowData = [
        row.site_name,
        siteType,
        row.site_dr,
        formatVisits(row.site_visits),
        row.site_url,
        row.site_submit_url,
        screenshotUrls[0],
        screenshotUrls[1],
        screenshotUrls[2],
        screenshotUrls[3]
    ];

    rowData.forEach((value, colIndex) => {
        const cell = sheet.getCell(rowIndex, colIndex + 1);

        // Handle Site URL column (index 4)
        if (colIndex === 4) {
            const url = value as string;
            if (url && url.length > 0) {
                cell.value = {
                    text: '[Site URL]',
                    hyperlink: url
                };
                cell.style = hyperlinkStyle;
            } else {
                cell.value = '';
            }
        }
        // Handle Submit URL column (index 5)
        else if (colIndex === 5) {
            const url = value as string;
            if (url && url.length > 0) {
                cell.value = {
                    text: '[Submit URL]',
                    hyperlink: url
                };
                cell.style = hyperlinkStyle;
            } else {
                cell.value = '';
            }
        }
        // Handle Screenshot columns (indices 6, 7, 8, 9)
        else if (colIndex >= 6 && colIndex <= 9) {
            const url = value as string;
            const screenshotNumber = colIndex - 5; // Convert to 1-based screenshot number
            if (url && url.length > 0) {
                cell.value = {
                    text: `[Screenshot ${screenshotNumber}]`,
                    hyperlink: url
                };
                cell.style = hyperlinkStyle;
            } else {
                cell.value = '';
            }
        }
        // Handle other columns normally
        else {
            cell.value = value as string | number;
        }
    });
}

export const POST: RequestHandler = async ({ request }) => {
    try {
        const req = await request.json();
        const reportId = req.report_id;

        if (!reportId) {
            return json({
                status: 'error',
                message: 'Invalid report ID'
            }, { status: 400 });
        }

        const report = await getReport(reportId);
        const labels = await getLabels();
        const { report_sites: reportSites } = await getReportSites(reportId);

        const workbook = createSheet();

        addInfo(workbook, report.report_website_url, report.report_email_address, report.report_email_password);

        addHeaders(workbook);

        let rowIndex = 5;
        reportSites.forEach((site) => {
          if (!site.checked) return;
          addRow(workbook, site, rowIndex, labels);
          rowIndex++;
        });

        const buffer = await workbook.xlsx.writeBuffer();

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `report-${timestamp}.xlsx`;

        return new Response(buffer, {
            status: 200,
            headers: {
                'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition': `attachment; filename="${filename}"`
            }
        });

    } catch (error) {
        console.error('Unexpected error at GenerateReport:', error);
        return json({
            status: 'error',
            message: `Unexpected error at GenerateReport: ${error instanceof Error ? error.message : 'Unknown error'}`
        }, { status: 500 });
    }
}