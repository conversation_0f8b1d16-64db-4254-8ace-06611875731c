<script lang="ts">
    import FormContainer from '$lib/components/form/FormContainer.svelte';
    import { goto } from '$app/navigation';
    import { toast } from 'svelte-sonner';
    import { onMount } from 'svelte';
    import type { GetFormFieldsResponse } from '$lib/types/responses';

    let reportId = $state<string>('');
    let fields = $state<Record<string, string>>({});

    let { data } = $props();

    reportId = data.report.report_id;

    onMount(async () => {
        if (!reportId) {
            toast.error('Report ID is required');
            goto('/dashboard');
            return;
        }

        await fetchFormFields();
    });

    async function fetchFormFields() {
        try {
            const response = await fetch(`/api/GetFormFields`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ report_id: reportId })
            });

            const data = await response.json() as GetFormFieldsResponse;

            if (data.status === 'success') {
                fields = data.fields || {};
                console.log("Fields:", fields);
            }
        } catch (error) {
            console.error('Error loading form fields:', error);
        }
    }
</script>

<section class="bg-secondary py-8 md:py-24 flex-1">
    <div class="container-xl">
        <h1 class="mb-5 font-bold text-white lg:mb-10 text-3xl lg:text-4xl">
          Report Form: {data.report.report_website_url}
        </h1>

        <div class="overflow-hidden rounded-lg bg-white ring-4 ring-white/25">
            <div class="p-4 md:p-6">
              <FormContainer
                fields={fields}
                editMode={true}
                reportId={reportId}
              />
            </div>
        </div>
    </div>
</section>
