import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { saveSiteVisits, parseVisitStr } from '$lib/services/extensions';
import { isValidURL } from '$lib/utils/validation';
import type { SaveVisitsRequest } from '$lib/types/requests';
import type { SaveVisitsResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
  try {
        const req = await request.json() as SaveVisitsRequest;
        const { visits, url } = req;

        if (!visits || !url) {
            return json({
                status: 'error',
                message: 'Visits and URL are required'
            } as SaveVisitsResponse, { status: 400 });
        }

        if (!isValidURL(url)) {
            return json({
                status: 'error',
                message: 'Invalid URL format'
            } as SaveVisitsResponse, { status: 400 });
        }

        const visitsNum = parseVisitStr(visits);

        await saveSiteVisits(visitsNum, url);

        return json({
            status: 'success',
            message: 'Visits saved successfully'
        } as SaveVisitsResponse, { status: 200 });
    } catch (error) {
        console.error('Unexpected error at SaveVisits:', error);
        return json({
            status: 'error',
            message: `Unexpected error at SaveVisits: ${error instanceof Error ? error.message : 'Unknown error'}`
        } as SaveVisitsResponse, { status: 500 });
    }
}