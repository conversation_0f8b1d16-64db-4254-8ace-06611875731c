import sql from '$lib/db/db';
import type { ReportSite } from '$lib/types/tables';
import type { ReportSiteWithLabels } from '$lib/types/services';

export async function getReportSites(
    reportId: string,
    labelIds?: string[],
    pageNum?: number,
    perPage?: number,
    sort?: string
): Promise<{ report_sites: ReportSiteWithLabels[]; total: number }> {

    labelIds = labelIds || [];
    pageNum = pageNum || 1;
    perPage = perPage || 20;
    sort = sort || 'dr';

    const validPageNum = pageNum <= 0 ? 1 : pageNum;
    const sortCol = sort === 'visits' ? 'site_visits' : 'site_dr';
    const offset = perPage > 0 ? (validPageNum - 1) * perPage : 0;

    const conditions: string[] = [];

    if (labelIds.length > 0) {
        conditions.push(`
            s.site_id IN (
                SELECT site_id
                FROM site_label_rels
                WHERE label_id IN ('${labelIds.join("','")}')
                GROUP BY site_id
                HAVING COUNT(DISTINCT label_id) = ${labelIds.length}
            )
        `);
    }

    let countQueryParts = [`
        SELECT COUNT(DISTINCT s.site_id) AS total_count
        FROM sites s
    `];

    if (conditions.length > 0) {
        countQueryParts.push('WHERE ' + conditions.join(' AND '));
    }

    const countResult = await sql.unsafe(countQueryParts.join(' '), []);
    const total = parseInt(countResult[0].total_count);

    // Use parameterized query instead of string interpolation for report_id
    const reportSites = await sql<ReportSiteWithLabels[]>`
        SELECT
            s.site_id, s.site_name, s.site_url, s.site_submit_url, s.site_actual_submit_url, s.site_note,
            s.site_waiting_time, s.site_created_at, s.site_dr, s.site_dr_updated_at,
            s.site_visits, s.site_visits_updated_at,
            COALESCE(rs.checked, FALSE) AS checked,
            COALESCE(rs.screenshot_urls, '{}') AS screenshot_urls,
            COALESCE(
                ARRAY_AGG(l.label_id) FILTER (WHERE l.label_id IS NOT NULL),
                '{}'::uuid[]
            ) AS label_ids
        FROM sites s
        LEFT JOIN report_sites rs ON s.site_id = rs.site_id AND rs.report_id = ${reportId}
        LEFT JOIN site_label_rels slr ON s.site_id = slr.site_id
        LEFT JOIN labels l ON slr.label_id = l.label_id
        ${conditions.length > 0 ? sql`WHERE ${sql.unsafe(conditions.join(' AND '))}` : sql``}
        GROUP BY s.site_id, s.site_name, s.site_url, s.site_submit_url, s.site_actual_submit_url, s.site_note,
                 s.site_waiting_time, s.site_created_at, s.site_dr, s.site_dr_updated_at,
                 s.site_visits, s.site_visits_updated_at,
                 rs.checked, rs.screenshot_urls
        ORDER BY ${sortCol === 'site_visits' ? sql`site_visits` : sql`site_dr`} DESC
        ${perPage > 0 ? sql`LIMIT ${perPage} OFFSET ${offset}` : sql``}
    `;

    return { report_sites: reportSites, total };
}

export async function getReportSite(reportId: string, siteId: string): Promise<ReportSiteWithLabels | null> {
    const result = await sql<ReportSiteWithLabels[]>`
        SELECT
            s.site_id, s.site_name, s.site_url, s.site_submit_url, s.site_actual_submit_url, s.site_note,
            s.site_waiting_time, s.site_created_at, s.site_dr, s.site_dr_updated_at,
            s.site_visits, s.site_visits_updated_at,
            COALESCE(rs.checked, FALSE) AS checked,
            COALESCE(rs.screenshot_urls, '{}') AS screenshot_urls,
            COALESCE(
                ARRAY_AGG(l.label_id) FILTER (WHERE l.label_id IS NOT NULL),
                '{}'::uuid[]
            ) AS label_ids
        FROM sites s
        LEFT JOIN report_sites rs ON s.site_id = rs.site_id AND rs.report_id = ${reportId}
        LEFT JOIN site_label_rels slr ON s.site_id = slr.site_id
        LEFT JOIN labels l ON slr.label_id = l.label_id
        WHERE s.site_id = ${siteId}
        GROUP BY s.site_id, s.site_name, s.site_url, s.site_submit_url, s.site_actual_submit_url, s.site_note,
                 s.site_waiting_time, s.site_created_at, s.site_dr, s.site_dr_updated_at,
                 s.site_visits, s.site_visits_updated_at,
                 rs.checked, rs.screenshot_urls
        LIMIT 1
    `;
    if (result.length === 0) {
        return null;
    }
    return result[0];
}

export async function saveReportSite(reportSite: ReportSite): Promise<void> {
    await sql`
        INSERT INTO report_sites (
            report_id,
            site_id,
            checked,
            screenshot_urls
        )
        VALUES (
            ${reportSite.report_id},
            ${reportSite.site_id},
            ${reportSite.checked},
            ${reportSite.screenshot_urls}
        )
        ON CONFLICT (report_id, site_id)
        DO UPDATE SET
            checked = EXCLUDED.checked,
            screenshot_urls = EXCLUDED.screenshot_urls
    `;
}
