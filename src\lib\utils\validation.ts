export function isValidURL(url: string): boolean {
    if (!url) return false;

    try {
        // Use the built-in URL constructor for more accurate validation
        // This handles all valid URL formats including hash fragments, query params, etc.
        const urlObj = new URL(url.startsWith('http://') || url.startsWith('https://') ? url : `https://${url}`);

        // Additional check to ensure it's a web URL (http or https)
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
        return false;
    }
}
