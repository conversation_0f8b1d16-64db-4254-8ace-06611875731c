<script lang="ts">
    import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
    import Head from "$lib/components/layout/Head.svelte";
    import CustomDropdown from '$lib/components/misc/CustomDropdown.svelte';
    import CustomInputText from '$lib/components/misc/CustomInputText.svelte';
    import * as Dialog from '$lib/components/ui/dialog/index';
    import { toast } from 'svelte-sonner';
    import { onMount } from 'svelte';
    import type { Label, SiteFormField } from '$lib/types/tables';
    import type { SiteWithLabelIds } from '$lib/types/services';
    import type { GetLabelsResponse } from '$lib/types/responses';
    import FormFieldEditor from '$lib/components/form-fields/FormFieldEditor.svelte';

    let sites = $state<SiteWithLabelIds[]>([]);
    let labels = $state<Label[]>([]);
    let labelsByGroup = $state<Record<string, Label[]>>({});
    let isLoading = $state(true);
    let searchQuery = $state('');
    let searchTimeout: ReturnType<typeof setTimeout> | null = null;

    let selectedSite = $state<SiteWithLabelIds | null>(null);
    let formFieldsDialogOpen = $state(false);
    let siteFormFields = $state<SiteFormField[]>([]);

    let selectOptions = $state<Record<string, string>>({
        type: '',
        pricing: '',
        link_type: '',
        login: '',
        author_info: '',
        confirmation: '',
        form_display: '',
        difficulty: '',
        field_type: '',
        match_report_field: ''
    });

    let groups: Array<{ key: string; value: string }> = [
        { key: 'type', value: 'Type' },
        { key: 'pricing', value: 'Pricing' },
        { key: 'status', value: 'Status' },
        { key: 'link-type', value: 'Link Type' },
        { key: 'login', value: 'Login' },
        { key: 'author-info', value: 'Author Info' },
        { key: 'confirmation', value: 'Confirmation' },
        { key: 'form-display', value: 'Form Display' },
        { key: 'difficulty', value: 'Difficulty' },
    ];

    // Field type options for dropdown
    const fieldTypeOptions = [
        { label_id: 'input-text', label_name: 'input-text' },
        { label_id: 'input-url', label_name: 'input-url' },
        { label_id: 'input-email', label_name: 'input-email' },
        { label_id: 'input-file', label_name: 'input-file' },
        { label_id: 'input-number', label_name: 'input-number' },
        { label_id: 'input-date', label_name: 'input-date' },
        { label_id: 'textarea', label_name: 'textarea' },
        { label_id: 'select', label_name: 'select' },
        { label_id: 'radio-group', label_name: 'radio-group' },
        { label_id: 'checkbox-group', label_name: 'checkbox-group' },
        { label_id: 'button', label_name: 'button' },
        { label_id: 'custom-input-text', label_name: 'custom-input-text' },
        { label_id: 'custom-input-url', label_name: 'custom-input-url' },
        { label_id: 'custom-input-email', label_name: 'custom-input-email' },
        { label_id: 'custom-input-file', label_name: 'custom-input-file' },
        { label_id: 'custom-input-number', label_name: 'custom-input-number' },
        { label_id: 'custom-input-date', label_name: 'custom-input-date' },
        { label_id: 'custom-textarea', label_name: 'custom-textarea' },
        { label_id: 'custom-select', label_name: 'custom-select' },
        { label_id: 'custom-radio-group', label_name: 'custom-radio-group' },
        { label_id: 'custom-checkbox-group', label_name: 'custom-checkbox-group' },
        { label_id: 'dragdrop-input-file', label_name: 'dragdrop-input-file' }
    ];

    // Match report field options for dropdown
    const matchReportFieldOptions = [
        // Author Information
        { label_id: 'profile-picture', label_name: 'profile-picture' },
        { label_id: 'first-name', label_name: 'first-name' },
        { label_id: 'last-name', label_name: 'last-name' },
        { label_id: 'your-occupation', label_name: 'your-occupation' },
        { label_id: 'your-phone-number', label_name: 'your-phone-number' },
        { label_id: 'your-biography', label_name: 'your-biography' },
        { label_id: 'number-of-team-members', label_name: 'number-of-team-members' },
        { label_id: 'your-email', label_name: 'your-email' },
        { label_id: 'your-personal-website', label_name: 'your-personal-website' },
        { label_id: 'your-x-twitter-url', label_name: 'your-x-twitter-url' },
        { label_id: 'your-youtube-url', label_name: 'your-youtube-url' },
        { label_id: 'your-linkedin-url', label_name: 'your-linkedin-url' },
        { label_id: 'your-instagram-url', label_name: 'your-instagram-url' },

        // Product Information
        { label_id: 'product-icon', label_name: 'product-icon' },
        { label_id: 'product-screenshot-01', label_name: 'product-screenshot-01' },
        { label_id: 'product-screenshot-02', label_name: 'product-screenshot-02' },
        { label_id: 'product-screenshot-03', label_name: 'product-screenshot-03' },
        { label_id: 'product-screenshot-04', label_name: 'product-screenshot-04' },
        { label_id: 'product-name', label_name: 'product-name' },
        { label_id: 'product-url', label_name: 'product-url' },
        { label_id: 'product-email', label_name: 'product-email' },
        { label_id: 'product-launch-date', label_name: 'product-launch-date' },
        { label_id: 'product-short-description', label_name: 'product-short-description' },
        { label_id: 'product-long-description', label_name: 'product-long-description' },
        { label_id: 'product-tagline', label_name: 'product-tagline' },
        { label_id: 'product-slogan', label_name: 'product-slogan' },
        { label_id: 'product-categories', label_name: 'product-categories' },
        { label_id: 'product-tags', label_name: 'product-tags' },
        { label_id: 'product-key-features', label_name: 'product-key-features' },
        { label_id: 'product-use-cases', label_name: 'product-use-cases' },
        { label_id: 'product-pros', label_name: 'product-pros' },
        { label_id: 'product-cons', label_name: 'product-cons' },
        { label_id: 'product-vision', label_name: 'product-vision' },
        { label_id: 'why-the-product-stands-out', label_name: 'why-the-product-stands-out' },
        { label_id: 'product-story', label_name: 'product-story' },
        { label_id: 'product-tech-stack', label_name: 'product-tech-stack' },

        // Pricing Models and Finances
        { label_id: 'product-pricing-model', label_name: 'product-pricing-model' },
        { label_id: 'product-pricing-information', label_name: 'product-pricing-information' },
        { label_id: 'product-mrr', label_name: 'product-mrr' },
        { label_id: 'product-total-funding', label_name: 'product-total-funding' },

        // Product Statistics
        { label_id: 'product-stage', label_name: 'product-stage' },
        { label_id: 'active-users', label_name: 'active-users' },
        { label_id: 'monthly-unique-visitors', label_name: 'monthly-unique-visitors' },
        { label_id: 'monthly-downloads', label_name: 'monthly-downloads' },
        { label_id: 'weekly-hours-dedicated-to-the-product', label_name: 'weekly-hours-dedicated-to-the-product' },

        // Social Media and Multimedia
        { label_id: 'product-facebook-url', label_name: 'product-facebook-url' },
        { label_id: 'product-x-twitter-url', label_name: 'product-x-twitter-url' },
        { label_id: 'product-linkedin-url', label_name: 'product-linkedin-url' },
        { label_id: 'product-instagram-url', label_name: 'product-instagram-url' },
        { label_id: 'product-youtube-url', label_name: 'product-youtube-url' },
        { label_id: 'product-github-url', label_name: 'product-github-url' },
        { label_id: 'product-demo-video-url', label_name: 'product-demo-video-url' },
        { label_id: 'product-explainer-video-url', label_name: 'product-explainer-video-url' },

        // Website Information
        { label_id: 'pricing-page-url', label_name: 'pricing-page-url' },
        { label_id: 'faq-page-url', label_name: 'faq-page-url' },
        { label_id: 'blog-page-url', label_name: 'blog-page-url' },
        { label_id: 'terms-page-url', label_name: 'terms-page-url' },
        { label_id: 'privacy-page-url', label_name: 'privacy-page-url' },
        { label_id: 'contact-page-url', label_name: 'contact-page-url' },
        { label_id: 'documentation-page-url', label_name: 'documentation-page-url' },

        // Product Location Information
        { label_id: 'product-country', label_name: 'product-country' },
        { label_id: 'product-state', label_name: 'product-state' },
        { label_id: 'product-city', label_name: 'product-city' },
        { label_id: 'product-postal-code', label_name: 'product-postal-code' },
        { label_id: 'product-address', label_name: 'product-address' }
    ];

    async function fetchLabels(): Promise<boolean> {
        try {
            const response = await fetch('/api/GetLabels', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });

            const data = await response.json() as GetLabelsResponse;

            if (data.status === 'success') {
                labels = data.labels;

                labelsByGroup = {};
                for (const group of groups) {
                    labelsByGroup[group.key] = labels.filter(label => label.label_group === group.key);
                }

                return true;
            }
            return false;
        } catch (error) {
            console.error('Error fetching labels:', error);
            return false;
        }
    }

    function getSelectedLabelIds(): string[] {
        const selectedIds: string[] = [];

        // Always include "Approved" status
        const approvedLabel = labelsByGroup['status']?.find(label => label.label_name === 'Approved');
        if (approvedLabel) {
            selectedIds.push(approvedLabel.label_id);
        }

        for (const [groupKey, selectedValue] of Object.entries(selectOptions)) {
            if (selectedValue && groupKey !== 'field_type' && groupKey !== 'match_report_field') {
                const groupLabels = labelsByGroup[groupKey.replace('_', '-')] || [];
                const selectedLabel = groupLabels.find(label => label.label_id === selectedValue);
                if (selectedLabel) {
                    selectedIds.push(selectedLabel.label_id);
                }
            }
        }

        return selectedIds;
    }

    async function getSites() {
        try {
            isLoading = true;
            const selectedLabelIds = getSelectedLabelIds();

            const sitesRequest = {
                andLabelIds: selectedLabelIds,
                search: searchQuery && searchQuery.length >= 2 ? searchQuery : '',
                field_type: selectOptions.field_type || '',
                match_report_field: selectOptions.match_report_field || ''
            };

            const response = await fetch(`/api/GetSitesWithFormFields`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(sitesRequest)
            });
            const data = await response.json();

            if (data.status === 'success') {
                sites = data.sites;
            } else {
                toast.error(data.message || 'Failed to fetch sites');
            }
        } catch (error) {
            console.error('Error fetching sites:', error);
            toast.error('Failed to fetch sites');
        } finally {
            isLoading = false;
        }
    }

    function handleSearch(event: Event): void {
        const input = event.target as HTMLInputElement;
        searchQuery = input.value;

        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        if (searchQuery.length >= 2 || searchQuery.length === 0) {
            searchTimeout = setTimeout(() => {
                getSites();
            }, 500);
        }
    }

    function openFormFieldsModal(site: SiteWithLabelIds) {
        selectedSite = site;

        try {
            if (!site.site_form_fields) {
                siteFormFields = [];
            } else if (typeof site.site_form_fields === 'string') {
                siteFormFields = JSON.parse(site.site_form_fields);
            } else if (Array.isArray(site.site_form_fields)) {
                siteFormFields = site.site_form_fields;
            } else {
                console.error('Unexpected form fields type:', typeof site.site_form_fields, site.site_form_fields);
                siteFormFields = [];
            }
        } catch (error) {
            console.error('Error parsing form fields:', error);
            siteFormFields = [];
        }

        formFieldsDialogOpen = true;
    }

    async function saveFormFields(updatedFields: SiteFormField[]) {
        if (!selectedSite) return;

        try {
            const response = await fetch('/api/UpdateSiteFormFields', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    site_id: selectedSite.site_id,
                    form_fields: JSON.stringify(updatedFields)
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                toast.success('Form fields updated successfully');

                const siteIndex = sites.findIndex(s => s.site_id === selectedSite!.site_id);
                if (siteIndex !== -1) {
                    sites[siteIndex].site_form_fields = JSON.stringify(updatedFields);
                }

                formFieldsDialogOpen = false;
            } else {
                toast.error(data.message || 'Failed to update form fields');
            }
        } catch (error) {
            console.error('Error saving form fields:', error);
            toast.error('Failed to save form fields');
        }
    }

    onMount(async () => {
        const success = await fetchLabels();
        if (success) {
            await getSites();
        } else {
            toast.error('Failed to load labels');
            isLoading = false;
        }
    });
</script>

<Head
    title={`Form Fields - ${PUBLIC_SITE_NAME}`}
    description="Manage site form fields"
    url={`${PUBLIC_SITE_URL}/dashboard/form-fields`}
/>

<section class="flex-1 bg-secondary py-8 lg:py-24">
    <div class="container-xl">
        <div class="mb-12 flex items-center justify-between">
            <h1 class="text-3xl font-bold tracking-tight text-white md:text-4xl">Form Fields</h1>
        </div>

        <div class="relative overflow-hidden rounded-lg bg-white ring-4 ring-white/25">
            <div class="border-b border-gray-300 p-4">
                <CustomInputText
                    name="site-search"
                    title="Search"
                    type="search"
                    placeholder="Search sites..."
                    value={searchQuery}
                    onInput={handleSearch}
                    error={""}
                />
            </div>

            <div class="flex flex-col items-start justify-between gap-4 border-b border-gray-300 p-4 lg:flex-row lg:items-center">
                <div class="grid w-full gap-4 sm:grid-cols-2 md:grid-cols-4">
                    <CustomDropdown
                        onChange={() => {
                            getSites();
                        }}
                        bind:value={selectOptions.type}
                        labels={labelsByGroup['type']}
                        placeholder="All"
                        title="Type:"
                    />

                    <CustomDropdown
                        onChange={() => {
                            getSites();
                        }}
                        bind:value={selectOptions.pricing}
                        labels={labelsByGroup['pricing']}
                        placeholder="All"
                        title="Pricing:"
                    />

                    <CustomDropdown
                        onChange={() => {
                            getSites();
                        }}
                        bind:value={selectOptions.field_type}
                        labels={fieldTypeOptions}
                        placeholder="All"
                        title="Field Type:"
                    />

                    <CustomDropdown
                        onChange={() => {
                            getSites();
                        }}
                        bind:value={selectOptions.match_report_field}
                        labels={matchReportFieldOptions}
                        placeholder="All"
                        title="Match Report Field:"
                    />
                </div>
            </div>

            {#if isLoading}
                <div class="flex items-center justify-center p-8">
                    <div class="text-gray-500">Loading sites...</div>
                </div>
            {:else if sites.length === 0}
                <div class="flex items-center justify-center p-8">
                    <div class="text-gray-500">No sites found</div>
                </div>
            {:else}
                <div class="p-4">
                    <div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                        {#each sites as site}
                            <div class="relative rounded-lg border border-gray-200 p-4 transition-all hover:border-gray-300 hover:shadow-md">
                                <button
                                    class="absolute top-3 right-3 p-1 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
                                    onclick={() => openFormFieldsModal(site)}
                                    aria-label="Edit form fields"
                                >
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                </button>

                                <h3 class="font-semibold text-gray-900 truncate pr-8">{site.site_name || 'Unnamed Site'}</h3>

                                <div class="mt-2 flex flex-wrap gap-2">
                                    {#if site.site_url}
                                        <a href={site.site_url} target="_blank" class="text-sm text-blue-600 hover:text-blue-800">
                                            Site URL
                                        </a>
                                    {/if}
                                    {#if site.site_submit_url}
                                        <span class="text-gray-400">|</span>
                                        <a href={site.site_submit_url} target="_blank" class="text-sm text-blue-600 hover:text-blue-800">
                                            Submit URL
                                        </a>
                                    {/if}
                                    {#if site.site_actual_submit_url}
                                        <span class="text-gray-400">|</span>
                                        <a href={site.site_actual_submit_url} target="_blank" class="text-sm text-blue-600 hover:text-blue-800">
                                            Actual Submit URL
                                        </a>
                                    {/if}
                                </div>

                                <div class="mt-3 flex items-center justify-between">
                                    <span class="text-xs text-gray-500">
                                        {(() => {
                                            try {
                                                if (!site.site_form_fields) return '0 fields';
                                                if (typeof site.site_form_fields === 'string') {
                                                    const parsed = JSON.parse(site.site_form_fields);
                                                    return `${Array.isArray(parsed) ? parsed.length : 0} fields`;
                                                } else if (Array.isArray(site.site_form_fields)) {
                                                    return `${(site.site_form_fields as any[]).length} fields`;
                                                } else {
                                                    return '0 fields';
                                                }
                                            } catch (error) {
                                                console.error('Error parsing form fields for site:', site.site_id, error);
                                                return 'Invalid JSON';
                                            }
                                        })()}
                                    </span>
                                    <span class="text-xs text-gray-500">DR: {site.site_dr || 0}</span>
                                </div>
                            </div>
                        {/each}
                    </div>
                </div>
            {/if}
        </div>
    </div>
</section>

<Dialog.Root bind:open={formFieldsDialogOpen}>
    <Dialog.Content class="sm:max-w-[95vw] max-h-[95vh] flex flex-col">
        <Dialog.Header class="flex-shrink-0">
            <Dialog.Title>Form Fields - {selectedSite?.site_name || 'Unknown Site'}</Dialog.Title>
            <Dialog.Description>
                Edit the form fields configuration for this site.
            </Dialog.Description>
        </Dialog.Header>

        <div class="flex-1 overflow-y-auto min-h-0">
            {#if selectedSite}
                <FormFieldEditor
                    bind:formFields={siteFormFields}
                    onSave={saveFormFields}
                />
            {/if}
        </div>
    </Dialog.Content>
</Dialog.Root>
