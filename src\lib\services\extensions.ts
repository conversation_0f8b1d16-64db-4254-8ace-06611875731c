import sql from '$lib/db/db';

export async function getFirstURL(sortby: 'dr' | 'visits', labelIds?: string[]): Promise<string> {
  const oneMonthAgo = Math.floor(Date.now() / 1000 - 30 * 24 * 60 * 60);
  const currentTime = Math.floor(Date.now() / 1000);

  // Base query parts
  let queryParts = [];
  let params: any[] = [];
  let paramIndex = 1;

  if (sortby === 'dr') {
    queryParts.push(`
      SELECT s.site_id, s.site_url
      FROM sites s
      WHERE s.site_dr_updated_at < ${oneMonthAgo}
    `);
  } else if (sortby === 'visits') {
    queryParts.push(`
      SELECT s.site_id, s.site_url
      FROM sites s
      WHERE s.site_visits_updated_at < ${oneMonthAgo}
    `);
  } else {
    throw new Error("invalid value for 'sortby'; expected 'dr' or 'visits'");
  }

  // Add label filtering if labelIds are provided
  if (labelIds && labelIds.length > 0) {
    queryParts.push(`
      AND s.site_id IN (
        SELECT site_id
        FROM site_label_rels
        WHERE label_id IN (${labelIds.map(() => `$${paramIndex++}`).join(', ')})
      )
    `);
    params.push(...labelIds);
  }

  // Add ordering and limit
  queryParts.push(`
    ORDER BY s.site_created_at DESC
    LIMIT 1
  `);

  const query = queryParts.join(' ');
  const result = await sql.unsafe<{ site_id: string, site_url: string }[]>(query, params);

  if (result.length === 0) {
    throw new Error('no sites found');
  }

  const siteURL = result[0].site_url;

  if (sortby === 'visits') {
    const siteId = result[0].site_id;

    const updateResult = await sql`
      UPDATE sites
      SET
        site_dr_updated_at = ${currentTime}
      WHERE site_id = ${siteId}
      RETURNING site_id
    `;

    if (updateResult.length === 0) {
      throw new Error('no site found with the provided URL');
    }
  }

  return siteURL;
}

export async function saveSiteDR(dr: number, url: string): Promise<void> {
  const currentTime = Math.floor(Date.now() / 1000);
  const likePattern = `%${url}%`;

  const result = await sql`
    UPDATE sites
    SET
      site_dr = ${dr},
      site_dr_updated_at = ${currentTime}
    WHERE site_url LIKE ${likePattern}
    RETURNING site_id
  `;

  if (result.length === 0) {
    throw new Error('no site found with the provided URL');
  }
}

export async function saveSiteVisits(visits: number, url: string): Promise<void> {
  const currentTime = Math.floor(Date.now() / 1000);
  const likePattern = `%${url}%`;

  const result = await sql`
    UPDATE sites
    SET
      site_visits = ${visits},
      site_visits_updated_at = ${currentTime}
    WHERE site_url LIKE ${likePattern}
    RETURNING site_id
  `;

  if (result.length === 0) {
    throw new Error('no site found matching the provided URL pattern');
  }
}

export function parseVisitStr(str: string): number {
  if (!str || typeof str !== 'string') {
    return 0;
  }

  if (str.trim().replace(/\s+/g, '') === '--') {
    return -1;
  }

  const normalized = str.trim().toUpperCase();

  const match = normalized.match(/^([-+]?\d*\.?\d+)\s*([KMB])?$/);

  if (!match) {
    const numericOnly = str.replace(/[^\d.-]/g, '');
    const fallbackNum = parseFloat(numericOnly);
    return isNaN(fallbackNum) ? 0 : fallbackNum;
  }

  const baseNumber = parseFloat(match[1]);
  const suffix = match[2];

  if (isNaN(baseNumber)) {
    return 0;
  }

  switch (suffix) {
    case 'K':
      return baseNumber * 1000;
    case 'M':
      return baseNumber * 1000000;
    case 'B':
      return baseNumber * 1000000000;
    default:
      return baseNumber;
  }
}

export function extractDomain(inputURL: string): string {
  try {
    const parsedURL = new URL(inputURL);
    const hostname = parsedURL.hostname;
    const parts = hostname.split('.');

    if (parts.length < 2) {
      return hostname;
    }

    if (parts[0] === 'www') {
      return parts.slice(1).join('.');
    }

    return hostname;
  } catch (error) {
    throw new Error(`Invalid URL: ${error instanceof Error ? error.message : String(error)}`);
  }
}