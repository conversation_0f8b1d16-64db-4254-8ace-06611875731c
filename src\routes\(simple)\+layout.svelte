<script lang="ts">
	import '../../app.css';
	let { children } = $props();

	import Header from "$lib/components/layout/HeaderForm.svelte";
	import Footer from "$lib/components/layout/FooterApp.svelte";
	import SVGIcons from "$lib/components/layout/SVGIcons.svelte";

  import { Toaster } from '$lib/components/ui/sonner';
</script>

<Header />
<main class="flex flex-1 flex-col">
    {@render children()}
</main>
<Footer />
<SVGIcons />

<Toaster theme="light" richColors position="bottom-center" />
