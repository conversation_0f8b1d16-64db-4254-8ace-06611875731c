<script>
    import { PUBLIC_SITE_NAME } from "$lib/utils/env";
    let isMenuOpen = false;
</script>

<header class="py-6 bg-blue300">
    <nav class="container">
        <div class="flex flex-wrap items-center justify-between">
            <a href="/" class="flex items-center">
                <img src="/assets/images/logo.svg" class="h-7 sm:h-8" alt={ PUBLIC_SITE_NAME } />
            </a>
            <div class="flex items-center lg:order-2">
                <a href="/" class="hidden btn-white btn-sm sm:flex w-fit shrink-0">
                  Home
                </a>
                <button onclick={() => { isMenuOpen = !isMenuOpen }} type="button" class="inline-flex items-center p-2 text-sm text-gray-500 rounded-lg lg:hidden focus:outline-none focus:ring-2 focus:ring-gray-200">
                  <span class="sr-only">Open main menu</span>
                  <svg class="w-6 h-6" fill="white" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>
                  <svg class="hidden w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                </button>
            </div>
            <div class="{isMenuOpen ? 'block' : 'hidden'} items-center justify-between w-full lg:flex lg:w-auto lg:order-1">
              <ul class="flex flex-col lg:flex-row lg:space-x-8 mt-2 lg:mt-0 w-full">
                <li>
                  <a href="/dashboard" class="block py-2 font-semibold text-white border-b border-white/50 lg:border-0 lg:p-0">Dashboard</a>
                </li>
                <li>
                  <a href="/dashboard/reports" class="block py-2 font-semibold text-white border-b border-white/50 lg:border-0 lg:p-0">Reports</a>
                </li>
                <li>
                  <a href="/dashboard/notes" class="block py-2 font-semibold text-white border-b border-white/50 lg:border-0 lg:p-0">Notes</a>
                </li>
                <li>
                  <a href="/dashboard/form-fields" class="block py-2 font-semibold text-white border-b border-white/50 lg:border-0 lg:p-0">Form Fields</a>
                </li>
                <li>
                  <a href="/dashboard/tools" class="block py-2 font-semibold text-white lg:border-0 lg:p-0">Tools</a>
                </li>
              </ul>
            </div>
        </div>
    </nav>
</header>