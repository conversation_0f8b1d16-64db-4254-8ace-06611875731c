import type { Label, Report, Note } from './tables';
import type { ReportSiteWithLabels, SiteWithLabelIds } from './services';
import type { ReportSite } from './tables';

export interface BaseResponse {
    status: 'success' | 'error';
    message: string;
}

export interface GetSitesResponse extends BaseResponse {
    sites: SiteWithLabelIds[];
    site_count: number;
    page_num: number;
    per_page: number;
}

export interface GetLabelsResponse extends BaseResponse {
    labels: Label[];
}

export interface SaveLabelResponse extends BaseResponse {
    label: Label;
}

export interface DeleteLabelResponse extends BaseResponse {
    // No additional properties
}

export interface GetReportResponse extends BaseResponse {
    report?: Report | null;
    report_sites?: ReportSiteWithLabels[];
    total?: number;
}

export interface GetReportsResponse extends BaseResponse {
    reports: Report[];
}

export interface GetReportSiteResponse extends BaseResponse {
    report_site?: ReportSiteWithLabels;
}

export interface GetReportSitesResponse extends BaseResponse {
    report_sites: ReportSiteWithLabels[];
}

export interface GetFormFieldsResponse extends BaseResponse {
    fields?: Record<string, string>;
}

export interface CreateSiteResponse extends BaseResponse {
    site: SiteWithLabelIds;
}

export interface UpdateSiteResponse extends BaseResponse {
    site: SiteWithLabelIds;
}

export interface DeleteSiteResponse extends BaseResponse {
    // No additional properties
}

export interface SaveReportResponse extends BaseResponse {
    report: Report;
}

export interface DeleteReportResponse extends BaseResponse {
    // No additional properties
}

export interface SaveReportSiteResponse extends BaseResponse {
    report_site: ReportSite;
}

export interface LoginResponse extends BaseResponse {
    token: string;
    user: {
        username: string;
        role: string;
    };
}

export interface SubmitFormResponse extends BaseResponse {
    reportId: string;
}

export interface UploadImageResponse extends BaseResponse {
    image_url: string;
}

export interface UploadFileResponse extends BaseResponse {
    file_url: string;
    file_name: string;
    file_size: number;
    file_type: string;
}

export interface GenerateReportResponse extends BaseResponse {
    report_url?: string;
}

export interface GetSiteJSONResponse extends BaseResponse {
    site_id: string;
    json_data?: string | null;
}

export interface SaveSiteJSONResponse extends BaseResponse {
    // No specific data needed beyond status and message
}

export interface SubmitURLExistsResponse extends BaseResponse {
    exists: boolean;
}

//

export interface GetURLResponse extends BaseResponse {
    url: string;
}

export interface SaveDRResponse extends BaseResponse {
    // No specific data needed beyond status and message
}

export interface SaveVisitsResponse extends BaseResponse {
    // No specific data needed beyond status and message
}

export interface GetNotesResponse extends BaseResponse {
    notes: Note[];
}

export interface SaveNoteResponse extends BaseResponse {
    note: Note;
}

export interface DeleteNoteResponse extends BaseResponse {
    // No additional properties
}

export interface UpdateNoteStatusResponse extends BaseResponse {
    note?: Note;
}

export interface DeleteFileResponse extends BaseResponse {
    // No additional properties
}

export interface UpdateSiteFormFieldsResponse extends BaseResponse {
    // No additional properties
}

export interface ValidateFormFieldsJSONResponse extends BaseResponse {
    results: Array<{
        site_id: string;
        site_name: string;
        site_url: string;
        status: 'valid' | 'invalid' | 'empty';
        error?: string;
        fieldCount?: number;
    }>;
}

export interface FixFormFieldsJSONResponse extends BaseResponse {
    // No additional properties
}