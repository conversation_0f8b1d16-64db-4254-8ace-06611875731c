<script lang="ts">
    import FormContainer from '$lib/components/form/FormContainer.svelte';
    import Head from "$lib/components/layout/Head.svelte";
    import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
</script>

<Head
    title={`Example Form - ${PUBLIC_SITE_NAME}`}
    description="Example of the SaaS submission form (for demonstration only)"
    url={`${PUBLIC_SITE_URL}/example-form`}
/>

<section class="bg-secondary py-8 md:py-24">
  <div class="container">
    <div class="mb-8 p-4 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700">
      <h2 class="text-xl font-bold mb-2">Example Form</h2>
      <p>This is a demonstration version of our form. You can explore all fields and see how the form works, but submission is disabled.</p>
    </div>

    <div class="mb-16">
      <h1 class="mb-5 font-bold text-white lg:mb-10 text-3xl lg:text-4xl">
        Registration Form for Your SaaS Product
      </h1>
      <p class="text-white md:text-lg">
        Hello! 👋
        <br />
        <br />
        Before you start filling out the form, I want to let you know that if you want your SaaS product to be listed
        in 60+ directories, it is essential that you fill out all the fields correctly.
        <br />
        <br />
        📋✨ Each platform has specific information requirements.
        <br />
        <br />
        Please note, if some information is missing and it's required for some directories, we will unfortunately
        not be able to register your SaaS in those directories. So make sure you have everything ready. 🚀
        <br />
        <br />
        Thank you for your attention! 😊
      </p>
    </div>

    <FormContainer
      fields={{}}
      editMode={false}
      reportId=""
      isExampleMode={true}
    />
  </div>
</section>
