import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { createSite, siteExists } from '$lib/services/sites';
import { createSiteLabels } from '$lib/services/site_label_rels';
import { isValidURL } from '$lib/utils/validation';
import type { CreateSiteRequest } from '$lib/types/requests';
import type { SiteWithLabelIds } from '$lib/types/services';
import type { CreateSiteResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const siteRequest = await request.json() as CreateSiteRequest;

        if (!siteRequest.site_url) {
            return json({
                status: "error",
                message: "Site URL is required"
            } as CreateSiteResponse, { status: 400 });
        }

        if (!isValidURL(siteRequest.site_url)) {
            return json({
                status: "error",
                message: "Invalid site URL format"
            } as CreateSiteResponse, { status: 400 });
        }

        if (siteRequest.site_submit_url && !isValidURL(siteRequest.site_submit_url)) {
            return json({
                status: "error",
                message: "Invalid site submit URL format"
            } as CreateSiteResponse, { status: 400 });
        }

        if (siteRequest.site_actual_submit_url && !isValidURL(siteRequest.site_actual_submit_url)) {
            return json({
                status: "error",
                message: "Invalid site actual submit URL format"
            } as CreateSiteResponse, { status: 400 });
        }

        const exists = await siteExists(siteRequest.site_url);
        if (exists) {
            return json({
                status: "error",
                message: "Site already exists with this URL"
            } as CreateSiteResponse, { status: 409 });
        }

        const site = {
            site_name: siteRequest.site_name,
            site_url: siteRequest.site_url,
            site_submit_url: siteRequest.site_submit_url,
            site_actual_submit_url: siteRequest.site_actual_submit_url,
            site_note: siteRequest.site_note,
            site_waiting_time: siteRequest.site_waiting_time
        };

        const siteId = await createSite(site);

        console.log("siteId", siteId);

        if (siteRequest.label_ids && siteRequest.label_ids.length > 0) {
            await createSiteLabels(siteId, siteRequest.label_ids);
        }

        const currentTime = Math.floor(Date.now() / 1000);
        const siteWithLabelIds: SiteWithLabelIds = {
            site_id: siteId,
            site_name: siteRequest.site_name,
            site_url: siteRequest.site_url,
            site_submit_url: siteRequest.site_submit_url,
            site_actual_submit_url: siteRequest.site_actual_submit_url,
            site_note: siteRequest.site_note,
            site_waiting_time: siteRequest.site_waiting_time,
            site_created_at: currentTime,
            site_dr: 0.0,
            site_visits: 0,
            label_ids: siteRequest.label_ids || []
        };

        return json({
            status: "success",
            message: "Site created successfully",
            site: siteWithLabelIds
        } as CreateSiteResponse, { status: 201 });
    } catch (err) {
        console.error('Unexpected error at CreateSite:', err);
        return json({
            status: "error",
            message: `Unexpected error at CreateSite: ${err instanceof Error ? err.message : 'Unknown error'}`
        } as CreateSiteResponse, { status: 500 });
    }
}
