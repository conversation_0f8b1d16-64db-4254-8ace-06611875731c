import sql from '$lib/db/db';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import type { UpdateSiteRequest } from '$lib/types/requests';
import { createSiteLabels, deleteSiteLabels } from '$lib/services/site_label_rels';
import type { UpdateSiteResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const site = await request.json() as UpdateSiteRequest;

        const result = await sql`
            UPDATE sites SET
                site_name = ${site.site_name},
                site_url = ${site.site_url},
                site_submit_url = ${site.site_submit_url},
                site_actual_submit_url = ${site.site_actual_submit_url},
                site_note = ${site.site_note},
                site_waiting_time = ${site.site_waiting_time}
            WHERE site_id = ${site.site_id}
            RETURNING *
        `;

        if (result.length === 0) {
            return json({
                status: "error",
                message: "Site not found"
            } as UpdateSiteResponse, { status: 404 });
        }

        await deleteSiteLabels(site.site_id);
        await createSiteLabels(site.site_id, site.label_ids);

        return json({
            status: "success",
            message: "Site updated successfully",
            site: site
        } as UpdateSiteResponse, { status: 200 });
    } catch (err) {
        console.error('Unexpected error at UpdateSite:', err);
        return json({
            status: "error",
            message: `Unexpected error at UpdateSite: ${err instanceof Error ? err.message : 'Unknown error'}`
        } as UpdateSiteResponse, { status: 500 });
    }
}