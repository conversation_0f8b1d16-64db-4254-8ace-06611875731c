import sql from '$lib/db/db';

export async function saveFormFields(reportId: string, fields: Record<string, string>): Promise<void> {
    // Create a copy of the fields to avoid modifying the original object
    const fieldsToSave = { ...fields };

    // Add derived fields
    // Always add full-name field, concatenating first-name and last-name when available
    const firstName = fieldsToSave['first-name'] || '';
    const lastName = fieldsToSave['last-name'] || '';

    // If both first and last name exist, concatenate them with a space
    if (firstName && lastName) {
        fieldsToSave['full-name'] = `${firstName} ${lastName}`;
    }
    // If only first name exists
    else if (firstName) {
        fieldsToSave['full-name'] = firstName;
    }
    // If only last name exists
    else if (lastName) {
        fieldsToSave['full-name'] = lastName;
    }
    // If neither exists, set to empty string
    else {
        fieldsToSave['full-name'] = '';
    }

    const values = Object.entries(fieldsToSave).map(([fieldName, fieldValue]) => ({
        report_id: reportId,
        field_name: fieldName,
        field_value: fieldValue
    }));

    if (values.length === 0) {
        throw new Error('No form fields provided to save');
    }

    await sql.begin(async (transaction) => {
        await transaction`
            INSERT INTO form_fields ${
                sql(values, 'report_id', 'field_name', 'field_value')
            }
            ON CONFLICT (report_id, field_name)
            DO UPDATE SET field_value = EXCLUDED.field_value
        `;
    });
}

export async function getFormFields(reportId: string): Promise<Record<string, string>> {
    const rows = await sql<{ field_name: string; field_value: string }[]>`
        SELECT field_name, field_value
        FROM form_fields
        WHERE report_id = ${reportId}
    `;

    return rows.reduce((acc, row) => {
        acc[row.field_name] = row.field_value;
        return acc;
    }, {} as Record<string, string>);
}