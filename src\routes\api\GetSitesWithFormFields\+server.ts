import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSitesWithFormFields } from '$lib/services/sites';
import type { BaseResponse } from '$lib/types/responses';
import type { SiteWithLabelIds } from '$lib/types/services';

export interface GetSitesWithFormFieldsRequest {
    andLabelIds?: string[];
    orLabelIds?: string[];
    search?: string;
    field_type?: string;
    match_report_field?: string;
}

export interface GetSitesWithFormFieldsResponse extends BaseResponse {
    sites: SiteWithLabelIds[];
}

export const POST: RequestHandler = async ({ request }) => {
    try {
        const body = await request.json() as GetSitesWithFormFieldsRequest;

        const result = await getSitesWithFormFields(
            body.andLabelIds,
            body.orLabelIds,
            body.search,
            body.field_type,
            body.match_report_field
        );

        return json({
            status: 'success',
            message: 'Sites retrieved successfully',
            sites: result.sites
        } as GetSitesWithFormFieldsResponse, { status: 200 });
    } catch (error) {
        console.error('Unexpected error at GetSitesWithFormFields:', error);
        return json({
            status: 'error',
            message: `Unexpected error at GetSitesWithFormFields: ${error instanceof Error ? error.message : 'Unknown error'}`,
            sites: []
        } as GetSitesWithFormFieldsResponse, { status: 500 });
    }
}
