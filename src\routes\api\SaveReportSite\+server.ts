import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { saveReportSite } from '$lib/services/report_sites';
import type { SaveReportSiteResponse } from '$lib/types/responses';
import type { SaveReportSiteRequest } from '$lib/types/requests';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const req = await request.json() as SaveReportSiteRequest;

    const reportId = req.report_id;
    const siteId = req.site_id;

    if (!reportId || !siteId) {
      return json({
        status: 'error',
        message: 'Report ID and site ID are required'
      } as SaveReportSiteResponse, { status: 400 });
    }



    const reportSite = {
      report_id: reportId,
      site_id: siteId,
      checked: req.checked || false,
      screenshot_urls: req.screenshot_urls || []
    };

    await saveReportSite(reportSite);

    return json({
      status: 'success',
      message: 'Report site created successfully',
      report_site: reportSite
    } as SaveReportSiteResponse, { status: 201 });
  } catch (error) {
    console.error('Unexpected error at saveReportSite:', error);
    return json({
      status: 'error',
      message: `Unexpected error at saveReportSite: ${error instanceof Error ? error.message : 'Unknown error'}`
    } as SaveReportSiteResponse, { status: 500 });
  }
}