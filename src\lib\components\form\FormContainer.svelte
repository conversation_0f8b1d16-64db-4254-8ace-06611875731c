<script lang="ts">
    import InputField from './InputField.svelte';
    import TextareaField from './TextareaField.svelte';
    import SelectField from './SelectField.svelte';
    import InputFieldFile from './InputFieldFile.svelte';
    import { toast } from 'svelte-sonner';
    import type { SubmitFormResponse } from '$lib/types/responses';
    import { goto } from '$app/navigation';

    let {
        fields = {} as Record<string, string>,
        editMode = false,
        reportId = '',
        isExampleMode = false
    } = $props();

    // Create default form fields
    const defaultFormFields = {
        // Author Information
        'profile-picture': '',
        'first-name': '',
        'last-name': '',
        'your-occupation': '',
        'your-phone-number': '',
        'your-biography': '',
        'number-of-team-members': '',
        'your-email': '',
        'your-personal-website': '',
        'your-x-twitter-url': '',
        'your-youtube-url': '',
        'your-linkedin-url': '',
        'your-instagram-url': '',

        // Product Information
        'product-icon': '',
        'product-screenshot-01': '',
        'product-screenshot-02': '',
        'product-screenshot-03': '',
        'product-screenshot-04': '',
        'product-name': '',
        'product-url': '',
        'product-email': '',
        'product-launch-date': '',
        'product-short-description': '',
        'product-long-description': '',
        'product-tagline': '',
        'product-slogan': '',
        'product-categories': '',
        'product-tags': '',
        'product-key-features': '',
        'product-use-cases': '',
        'product-pros': '',
        'product-cons': '',
        'product-vision': '',
        'why-the-product-stands-out': '',
        'product-story': '',
        'product-tech-stack': '',

        // Pricing Models and Finances
        'product-pricing-model': '',
        'product-pricing-information': '',
        'product-mrr': '',
        'product-total-funding': '',

        // Product Statistics
        'product-stage': '',
        'active-users': '',
        'monthly-unique-visitors': '',
        'monthly-downloads': '',
        'weekly-hours-dedicated-to-the-product': '',

        // Social Media and Multimedia
        'product-facebook-url': '',
        'product-x-twitter-url': '',
        'product-linkedin-url': '',
        'product-instagram-url': '',
        'product-youtube-url': '',
        'product-github-url': '',
        'product-demo-video-url': '',
        'product-explainer-video-url': '',

        // Website Information
        'pricing-page-url': '',
        'faq-page-url': '',
        'blog-page-url': '',
        'terms-page-url': '',
        'privacy-page-url': '',
        'contact-page-url': '',
        'documentation-page-url': '',

        // Product Location Information
        'product-country': '',
        'product-state': '',
        'product-city': '',
        'product-postal-code': '',
        'product-address': ''
    };

    // Initialize formFields with default values
    let formFields = $state<Record<string, string>>({...defaultFormFields});

    // Keep track of the previous fields to detect changes
    let previousFieldsJSON = $state('');

    // Update formFields when fields prop changes
    $effect(() => {
        // Only process if fields exist and have content
        if (fields && Object.keys(fields).length > 0) {
            // Convert current fields to JSON for comparison
            const currentFieldsJSON = JSON.stringify(fields);

            // Only update if the fields have changed
            if (currentFieldsJSON !== previousFieldsJSON) {
                console.log("Applying fields to form:", fields);
                formFields = { ...defaultFormFields, ...fields };
                previousFieldsJSON = currentFieldsJSON;
            }
        }
    });

    let errors = $state<Record<string, string>>({});
    let isLoading = $state<boolean>(false);

    function handleSuccess(reportId: string) {
        console.log('Form submitted successfully with report ID:', reportId);

        if (editMode) {
            // If in edit mode, just show a success message and stay on the page
            toast.success('Form updated successfully!');
        } else {
            // If creating a new form, redirect to thank you page after a short delay
            setTimeout(() => {
                goto(`/thank-you`);
            }, 500);
        }
    }

    function handleSubmit() {
        console.log('Submitting form with fields:', formFields);

        const requiredFields = [
            'profile-picture',
            'first-name',
            'last-name',
            'your-occupation',
            'number-of-team-members',
            'your-email',
            'your-x-twitter-url',
            'product-icon',
            'product-screenshot-01',
            'product-name',
            'product-url',
            'product-email',
            'product-short-description',
            'product-long-description',
            'product-tagline',
            'product-slogan',
            'product-categories',
            'product-tags',
            'product-key-features',
            'product-use-cases',
            'product-pros',
            'product-cons',
            'product-pricing-model',
            'product-mrr',
            'product-stage',
            'product-country'
        ];

        let hasErrors = false;
        errors = {};

        for (const field of requiredFields) {
            if (!formFields[field] || (typeof formFields[field] === 'string' && formFields[field].trim() === '')) {
                errors[field] = 'This field is required';
                hasErrors = true;
            }
        }

        if (hasErrors) {
            toast.error('Please fill in all required fields');
            return;
        }

        submitFormData(formFields);
    }

    async function submitFormData(formData: Record<string, string>) {
        try {
            isLoading = true;
            let submissionReportId: string;

            // If in edit mode, use the existing reportId
            if (editMode && reportId) {
                submissionReportId = reportId;
                console.log(`Updating existing report: ${reportId}`);
            } else {
                // Create a new report
                const productUrl = formData['product-url'] ?
                    `${formData['product-url']}` : 'New Submission';

                const createReportResponse = await fetch('/api/SaveReport', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        report_website_url: productUrl,
                        report_email_address: '',
                        report_email_password: ''
                    })
                });

                const createReportData = await createReportResponse.json();

                if (createReportData.status !== 'success' || !createReportData.report) {
                    toast.error('Failed to create a new report');
                    console.error('Failed to create report:', createReportData.message);
                    isLoading = false;
                    return;
                }

                submissionReportId = createReportData.report.report_id.toString();
            }

            // Submit the form data
            const response = await fetch('/api/SubmitForm', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    report_id: submissionReportId,
                    ...formData
                })
            });

            const data = await response.json() as SubmitFormResponse;

            if (data.status === 'success') {
                if (data.reportId) {
                    handleSuccess(data.reportId);
                }
            } else {
                toast.error(data.message || 'Failed to submit form');
            }
        } catch (error) {
            console.error('Error submitting form:', error);
            toast.error('Failed to submit form');
        } finally {
            isLoading = false;
        }
    }
</script>

<form id="main-form" class="flex flex-col gap-16 rounded-lg bg-white p-4 md:p-6">
    <div>
        <div class="mb-6 flex flex-col md:flex-row md:items-center justify-between">
            <h2 class="text-2xl font-semibold text-black md:text-3xl">Author Information</h2>
            <div class="text-lg font-semibold text-black md:text-xl">* = required</div>
        </div>
        <div class="grid gap-4 sm:grid-cols-2">
            <InputFieldFile name="profile-picture" label="Profile Picture" required={true} bind:value={formFields['profile-picture']} error={errors['profile-picture']} isExampleMode={isExampleMode} />
            <InputField name="first-name" label="First Name" placeholder="Enter your first name" fieldType="text" required={true} bind:value={formFields['first-name']} error={errors['first-name']} />
            <InputField name="last-name" label="Last Name" placeholder="Enter your last name" fieldType="text" required={true} bind:value={formFields['last-name']} error={errors['last-name']} />
            <InputField name="your-occupation" label="Your Occupation" placeholder="Enter your occupation" fieldType="text" required={true} bind:value={formFields['your-occupation']} error={errors['your-occupation']} />
            <InputField name="your-phone-number" label="Your Phone Number" placeholder="Enter your phone number" fieldType="text" required={false} bind:value={formFields['your-phone-number']} error={errors['your-phone-number']} />
            <TextareaField name="your-biography" label="Your Biography" placeholder="Enter a brief professional summary (e.g., 'Software engineer with 5 years of experience in AI development')" required={false} bind:value={formFields['your-biography']} error={errors['your-biography']} />
            <InputField name="number-of-team-members" label="Number of Team Members" placeholder="Enter number of members" fieldType="number" required={true} bind:value={formFields['number-of-team-members']} error={errors['number-of-team-members']} />
            <div></div>
            <InputField name="your-email" label="Your Email" placeholder="Enter your email" fieldType="email" required={true} bind:value={formFields['your-email']} error={errors['your-email']} />
            <InputField name="your-personal-website" label="Your Personal Website" placeholder="Enter your personal website" fieldType="url" required={false} bind:value={formFields['your-personal-website']} error={errors['your-personal-website']} />
            <InputField name="your-x-twitter-url" label="Your X / Twitter URL" placeholder="Enter your X/Twitter URL" fieldType="url" required={true} bind:value={formFields['your-x-twitter-url']} error={errors['your-x-twitter-url']} />
            <InputField name="your-youtube-url" label="Your YouTube Channel URL" placeholder="Enter your YouTube URL" fieldType="url" required={false} bind:value={formFields['your-youtube-url']} error={errors['your-youtube-url']} />
            <InputField name="your-linkedin-url" label="Your LinkedIn URL" placeholder="Enter your LinkedIn URL" fieldType="url" required={false} bind:value={formFields['your-linkedin-url']} error={errors['your-linkedin-url']} />
            <InputField name="your-instagram-url" label="Your Instagram URL" placeholder="Enter your Instagram URL" fieldType="url" required={false} bind:value={formFields['your-instagram-url']} error={errors['your-instagram-url']} />
        </div>
    </div>

    <div>
        <div class="mb-6 flex flex-col md:flex-row md:items-center justify-between">
            <h2 class="text-2xl font-semibold text-black md:text-3xl">Product Information</h2>
            <div class="text-lg font-semibold text-black md:text-xl">* = required</div>
        </div>
        <div class="grid gap-4 sm:grid-cols-2">
            <InputFieldFile name="product-icon" label="Product Icon" required={true} bind:value={formFields['product-icon']} error={errors['product-icon']} isExampleMode={isExampleMode} />
            <InputFieldFile name="product-screenshot-01" label="Product Screenshot. Add at Least 1" required={true} bind:value={formFields['product-screenshot-01']} error={errors['product-screenshot-01']} isExampleMode={isExampleMode} />
            <InputFieldFile name="product-screenshot-02" label="" required={false} bind:value={formFields['product-screenshot-02']} error={errors['product-screenshot-02']} isExampleMode={isExampleMode} />
            <InputFieldFile name="product-screenshot-03" label="" required={false} bind:value={formFields['product-screenshot-03']} error={errors['product-screenshot-03']} isExampleMode={isExampleMode} />
            <InputFieldFile name="product-screenshot-04" label="" required={false} bind:value={formFields['product-screenshot-04']} error={errors['product-screenshot-04']} isExampleMode={isExampleMode} />
            <InputField name="product-name" label="Product Name" placeholder="Enter your product name" fieldType="text" required={true} bind:value={formFields['product-name']} error={errors['product-name']} />
            <InputField name="product-url" label="Product URL" placeholder="Enter your product URL" fieldType="url" required={true} bind:value={formFields['product-url']} error={errors['product-url']} />
            <InputField name="product-email" label="Product Email" placeholder="Enter your product email" fieldType="email" required={true} bind:value={formFields['product-email']} error={errors['product-email']} />
            <InputField name="product-launch-date" label="Product Launch Date" placeholder="Enter your product launch date" fieldType="date" required={false} bind:value={formFields['product-launch-date']} error={errors['product-launch-date']} />

            <TextareaField name="product-short-description" label="Short Description of Your Product" placeholder="Enter a concise 1-2 sentence summary (e.g., 'An AI-powered task management tool for remote teams')" required={true} bind:value={formFields['product-short-description']} error={errors['product-short-description']} />
            <TextareaField name="product-long-description" label="Long Description of Your Product" placeholder="Provide a detailed explanation of what your product does and its benefits (e.g., features, problems it solves)" required={true} bind:value={formFields['product-long-description']} error={errors['product-long-description']} />
            <TextareaField name="product-tagline" label="Product Tagline" placeholder="Enter a catchy one-liner (e.g., 'Productivity reimagined')" required={true} bind:value={formFields['product-tagline']} error={errors['product-tagline']} />
            <TextareaField name="product-slogan" label="Product Slogan" placeholder="Enter a memorable phrase that captures your brand essence (e.g., 'Work smarter, not harder')" required={true} bind:value={formFields['product-slogan']} error={errors['product-slogan']} />
            <TextareaField name="product-categories" label="Categories (Separated by Commas)" placeholder="Enter relevant market categories (e.g., Productivity, SaaS, AI Tools, Remote Work)" required={true} bind:value={formFields['product-categories']} error={errors['product-categories']} />
            <TextareaField name="product-tags" label="Tags (Separated by Commas)" placeholder="Enter descriptive keywords (e.g., automation, collaboration, AI assistant, workflow)" required={true} bind:value={formFields['product-tags']} error={errors['product-tags']} />
            <TextareaField name="product-key-features" label="Key Features (Separated by Commas)" placeholder="List main features (e.g., 'AI task prioritization, team collaboration tools, progress analytics')" required={true} bind:value={formFields['product-key-features']} error={errors['product-key-features']} />
            <TextareaField name="product-use-cases" label="Use Cases (Separated by Commas)" placeholder="List scenarios where your product is useful (e.g., 'Remote team management, project planning, deadline tracking')" required={true} bind:value={formFields['product-use-cases']} error={errors['product-use-cases']} />
            <TextareaField name="product-pros" label="Pros" placeholder="List main advantages (e.g., 'Time-saving, intuitive interface, affordable pricing')" required={true} bind:value={formFields['product-pros']} error={errors['product-pros']} />
            <TextareaField name="product-cons" label="Cons" placeholder="List limitations (e.g., 'Limited mobile features, no offline mode')" required={true} bind:value={formFields['product-cons']} error={errors['product-cons']} />
            <TextareaField name="product-vision" label="Your Thoughts, Vision, and Anything You Want to Share About Your Product" placeholder="Share your product's purpose and future direction (e.g., 'We aim to revolutionize how remote teams collaborate')" required={false} bind:value={formFields['product-vision']} error={errors['product-vision']} />
            <TextareaField name="why-the-product-stands-out" label="Why The Product Stands Out" placeholder="Explain your unique value proposition (e.g., 'Only solution that combines AI analysis with team coordination')" required={false} bind:value={formFields['why-the-product-stands-out']} error={errors['why-the-product-stands-out']} />
            <TextareaField name="product-story" label="Product Story" placeholder="Share the journey behind creating your product (e.g., 'Founded after experiencing remote work challenges during 2020')" required={false} bind:value={formFields['product-story']} error={errors['product-story']} />
            <TextareaField name="product-tech-stack" label="What Technologies, Tools & Platforms Were Used to Build This Project?" placeholder="List technologies used (e.g., 'React, Node.js, MongoDB, AWS, TensorFlow, Docker, GraphQL')" required={false} bind:value={formFields['product-tech-stack']} error={errors['product-tech-stack']} />
        </div>
    </div>

    <div>
        <div class="mb-6 flex flex-col md:flex-row md:items-center justify-between">
            <h2 class="text-2xl font-semibold text-black md:text-3xl">Pricing Models and Finances</h2>
            <div class="text-lg font-semibold text-black md:text-xl">* = required</div>
        </div>
        <div class="grid gap-4 sm:grid-cols-2">
            <SelectField
                name="product-pricing-model"
                label="Product Pricing Model"
                options={{
                    "Free": "Free",
                    "Freemium": "Freemium",
                    "Paid": "Paid",
                }}
                required={true}
                bind:value={formFields['product-pricing-model']}
                error={errors['product-pricing-model']}
            />
            <div></div>
            <TextareaField name="product-pricing-information" label="Product Pricing Information" placeholder="Describe your pricing structure (e.g., 'Free tier plus $9/mo Pro and $19/mo Business plans')" required={false} bind:value={formFields['product-pricing-information']} error={errors['product-pricing-information']} />
            <InputField name="product-mrr" label="Product MRR" placeholder="Enter monthly recurring revenue (e.g., '$5,000' or 'Not applicable for new products')" fieldType="text" required={true} bind:value={formFields['product-mrr']} error={errors['product-mrr']} />
            <InputField name="product-total-funding" label="Total Amount of Funding" placeholder="Enter funding amount (e.g., '$250K seed funding' or 'Self-funded $50K')" fieldType="text" required={false} bind:value={formFields['product-total-funding']} error={errors['product-total-funding']} />
        </div>
    </div>

    <div>
        <div class="mb-6 flex flex-col md:flex-row md:items-center justify-between">
            <h2 class="text-2xl font-semibold text-black md:text-3xl">Product Statistics</h2>
            <div class="text-lg font-semibold text-black md:text-xl">* = required</div>
        </div>
        <div class="grid gap-4 sm:grid-cols-2">
            <SelectField
                name="product-stage"
                label="Current Stage of the Product"
                options={{
                    "Pending": "Pending",
                    "Under Development": "Under Development",
                    "Maintenance": "Maintenance",
                    "Launched": "Launched",
                }}
                required={true}
                bind:value={formFields['product-stage']}
                error={errors['product-stage']}
            />
            <InputField name="active-users" label="# of Active Users on Your Platform" placeholder="Enter number of active users" fieldType="number" required={false} bind:value={formFields['active-users']} error={errors['active-users']} />
            <InputField name="monthly-unique-visitors" label="Monthly Unique Visitors" placeholder="Enter average unique visitors" fieldType="number" required={false} bind:value={formFields['monthly-unique-visitors']} error={errors['monthly-unique-visitors']} />
            <InputField name="monthly-downloads" label="Monthly Downloads" placeholder="Enter average downloads" fieldType="number" required={false} bind:value={formFields['monthly-downloads']} error={errors['monthly-downloads']} />
            <InputField name="weekly-hours-dedicated-to-the-product" label="Weekly Hours Dedicated to the Product" placeholder="Enter hours per week" fieldType="number" required={false} bind:value={formFields['weekly-hours-dedicated-to-the-product']} error={errors['weekly-hours-dedicated-to-the-product']} />
        </div>
    </div>

    <div>
        <div class="mb-6 flex flex-col md:flex-row md:items-center justify-between">
            <h2 class="text-2xl font-semibold text-black md:text-3xl">Social Media and Multimedia</h2>
            <div class="text-lg font-semibold text-black md:text-xl">* = required</div>
        </div>
        <div class="grid gap-4 sm:grid-cols-2">
            <InputField name="product-facebook-url" label="Product Facebook URL" placeholder="Enter Facebook URL" fieldType="url" required={false} bind:value={formFields['product-facebook-url']} error={errors['product-facebook-url']} />
            <InputField name="product-x-twitter-url" label="Product X / Twitter URL" placeholder="Enter X/Twitter URL" fieldType="url" required={false} bind:value={formFields['product-x-twitter-url']} error={errors['product-x-twitter-url']} />
            <InputField name="product-linkedin-url" label="Product LinkedIn URL" placeholder="Enter LinkedIn URL" fieldType="url" required={false} bind:value={formFields['product-linkedin-url']} error={errors['product-linkedin-url']} />
            <InputField name="product-instagram-url" label="Product Instagram URL" placeholder="Enter Instagram URL" fieldType="url" required={false} bind:value={formFields['product-instagram-url']} error={errors['product-instagram-url']} />
            <InputField name="product-youtube-url" label="Product YouTube URL" placeholder="Enter YouTube URL" fieldType="url" required={false} bind:value={formFields['product-youtube-url']} error={errors['product-youtube-url']} />
            <InputField name="product-github-url" label="Product GitHub URL" placeholder="Enter GitHub URL" fieldType="url" required={false} bind:value={formFields['product-github-url']} error={errors['product-github-url']} />
            <InputField name="product-demo-video-url" label="Product Demo Video URL" placeholder="Enter demo video URL" fieldType="url" required={false} bind:value={formFields['product-demo-video-url']} error={errors['product-demo-video-url']} />
            <InputField name="product-explainer-video-url" label="Product Explainer Video URL" placeholder="Enter explainer video URL" fieldType="url" required={false} bind:value={formFields['product-explainer-video-url']} error={errors['product-explainer-video-url']} />
        </div>
    </div>

    <div>
        <div class="mb-6 flex flex-col md:flex-row md:items-center justify-between">
            <h2 class="text-2xl font-semibold text-black md:text-3xl">Website Information</h2>
            <div class="text-lg font-semibold text-black md:text-xl">* = required</div>
        </div>
        <div class="grid gap-4 sm:grid-cols-2">
            <InputField name="pricing-page-url" label="Pricing Page URL" placeholder="Enter pricing page URL" fieldType="url" required={false} bind:value={formFields['pricing-page-url']} error={errors['pricing-page-url']} />
            <InputField name="faq-page-url" label="FAQ Page URL" placeholder="Enter FAQ page URL" fieldType="url" required={false} bind:value={formFields['faq-page-url']} error={errors['faq-page-url']} />
            <InputField name="blog-page-url" label="Blog Page URL" placeholder="Enter blog page URL" fieldType="url" required={false} bind:value={formFields['blog-page-url']} error={errors['blog-page-url']} />
            <InputField name="terms-page-url" label="Terms Page URL" placeholder="Enter terms page URL" fieldType="url" required={false} bind:value={formFields['terms-page-url']} error={errors['terms-page-url']} />
            <InputField name="privacy-page-url" label="Privacy Page URL" placeholder="Enter privacy page URL" fieldType="url" required={false} bind:value={formFields['privacy-page-url']} error={errors['privacy-page-url']} />
            <InputField name="contact-page-url" label="Contact Page URL" placeholder="Enter contact page URL" fieldType="url" required={false} bind:value={formFields['contact-page-url']} error={errors['contact-page-url']} />
            <InputField name="documentation-page-url" label="Documentation Page URL" placeholder="Enter documentation page URL" fieldType="url" required={false} bind:value={formFields['documentation-page-url']} error={errors['documentation-page-url']} />
        </div>
    </div>

    <div>
        <div class="mb-6 flex flex-col md:flex-row md:items-center justify-between">
            <h2 class="text-2xl font-semibold text-black md:text-3xl">Product Location Information</h2>
            <div class="text-lg font-semibold text-black md:text-xl">* = required</div>
        </div>
        <div class="grid gap-4 sm:grid-cols-2">
            <InputField name="product-country" label="Country" placeholder="Enter a country" fieldType="text" required={true} bind:value={formFields['product-country']} error={errors['product-country']} />
            <InputField name="product-state" label="State / Province / Region" placeholder="Enter a state / province / region" fieldType="text" required={false} bind:value={formFields['product-state']} error={errors['product-state']} />
            <InputField name="product-city" label="City" placeholder="Enter a city" fieldType="text" required={false} bind:value={formFields['product-city']} error={errors['product-city']} />
            <InputField name="product-postal-code" label="ZIP Code / Postal Code" placeholder="Enter a ZIP code / postal code" fieldType="text" required={false} bind:value={formFields['product-postal-code']} error={errors['product-postal-code']} />
            <InputField name="product-address" label="Address" placeholder="Enter an address" fieldType="text" required={false} bind:value={formFields['product-address']} error={errors['product-address']} />
        </div>
    </div>

    <div class="flex items-center">
        {#if isExampleMode}
            <div class="flex flex-col gap-2">
                <button
                    type="button"
                    class="btn-primary opacity-50 cursor-not-allowed"
                    disabled={true}
                >
                    Example Form (Submission Disabled)
                </button>
                <p class="text-sm text-gray-600">This is an example form for demonstration purposes only.</p>
            </div>
        {:else}
            <button
                type="button"
                class="btn-primary disabled:opacity-50"
                onclick={handleSubmit}
                disabled={isLoading}
            >
                {isLoading ? 'Submitting...' : editMode ? 'Update Form' : 'Submit Form'}
            </button>
        {/if}
    </div>
</form>
