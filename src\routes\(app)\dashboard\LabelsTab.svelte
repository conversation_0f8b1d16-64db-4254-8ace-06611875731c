<script lang="ts">

    import { toast } from 'svelte-sonner';
    import * as Dialog from '$lib/components/ui/dialog/index';

    import type { Label } from '$lib/types/tables';
    import type { SaveLabelResponse, DeleteLabelResponse } from '$lib/types/responses';

    // Props
    let { labels, labelsByGroup, isLoading, onLabelsUpdated } = $props<{
        labels: Label[];
        labelsByGroup: Record<string, Label[]>;
        isLoading: boolean;
        onLabelsUpdated: () => Promise<boolean>;
    }>();

    // New label form
    let newLabel = $state<Partial<Label>>({
        label_name: '',
        label_group: '',
        label_color: '#000000',
        label_order: 0
    });

    // Edit label form
    let editLabel = $state<Label | null>(null);
    let editDialogOpen = $state(false);

    // Group options for dropdown
    const groupOptions = [
        { key: 'type', value: 'Type' },
        { key: 'pricing', value: 'Pricing' },
        { key: 'status', value: 'Status' },
        { key: 'link-type', value: 'Link Type' },
        { key: 'login', value: 'Login' },
        { key: 'author-info', value: 'Author Info' },
        { key: 'confirmation', value: 'Confirmation' },
        { key: 'form-display', value: 'Form Display' },
        { key: 'difficulty', value: 'Difficulty' },
    ];

    async function createLabel() {
        if (!newLabel.label_name) {
            toast.error('Label name is required');
            return;
        }

        if (!newLabel.label_group) {
            toast.error('Label group is required');
            return;
        }

        try {
            const response = await fetch('/api/SaveLabel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(newLabel)
            });

            const data = await response.json() as SaveLabelResponse;

            if (data.status === 'success') {
                toast.success(data.message || 'Label created successfully');

                // Reset form
                newLabel = {
                    label_name: '',
                    label_group: '',
                    label_color: '#000000',
                    label_order: 0
                };

                // Refresh labels using the parent function
                const success = await onLabelsUpdated();
                if (!success) {
                    toast.error('Failed to refresh labels');
                }
            } else {
                toast.error(data.message || 'Failed to create label');
            }
        } catch (error) {
            console.error('Error creating label:', error);
            toast.error('Failed to create label');
        }
    }

    async function updateLabel() {
        if (!editLabel) return;

        if (!editLabel.label_name) {
            toast.error('Label name is required');
            return;
        }

        if (!editLabel.label_group) {
            toast.error('Label group is required');
            return;
        }

        try {
            const response = await fetch('/api/SaveLabel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(editLabel)
            });

            const data = await response.json() as SaveLabelResponse;

            if (data.status === 'success') {
                toast.success(data.message || 'Label updated successfully');

                // Reset form and close dialog
                editLabel = null;
                editDialogOpen = false;

                // Refresh labels using the parent function
                const success = await onLabelsUpdated();
                if (!success) {
                    toast.error('Failed to refresh labels');
                }
            } else {
                toast.error(data.message || 'Failed to update label');
            }
        } catch (error) {
            console.error('Error updating label:', error);
            toast.error('Failed to update label');
        }
    }

    async function deleteLabel(label: Label) {
        if (!confirm(`Are you sure you want to delete the label "${label.label_name}"?`)) {
            return;
        }

        try {
            const response = await fetch('/api/DeleteLabel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ label_id: label.label_id })
            });

            const data = await response.json() as DeleteLabelResponse;

            if (data.status === 'success') {
                toast.success(data.message || 'Label deleted successfully');

                // Refresh labels using the parent function
                const success = await onLabelsUpdated();
                if (!success) {
                    toast.error('Failed to refresh labels');
                }
            } else {
                toast.error(data.message || 'Failed to delete label');
            }
        } catch (error) {
            console.error('Error deleting label:', error);
            toast.error('Failed to delete label');
        }
    }

    function openEditDialog(label: Label) {
        editLabel = { ...label };
        editDialogOpen = true;
    }

    // No need for onMount since labels are passed as props
</script>

<div class="mb-12 flex items-center justify-between">
    <h2 class="text-3xl font-bold tracking-tight text-white md:text-4xl">Labels</h2>
    <Dialog.Root>
        <Dialog.Trigger class="btn-primary w-fit">
            <svg class="h-4 w-4 fill-white">
                <use href="#icon-plus"></use>
            </svg>
            New Label
        </Dialog.Trigger>
        <Dialog.Content>
            <Dialog.Header>
                <h3 class="text-xl font-semibold">Create New Label</h3>
            </Dialog.Header>
            <div class="flex flex-col gap-4 py-4">
                <div>
                    <label for="label-name" class="mb-2 block text-sm font-medium">Label Name</label>
                    <input
                        id="label-name"
                        type="text"
                        class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                        bind:value={newLabel.label_name}
                    />
                </div>
                <div>
                    <label for="label-group" class="mb-2 block text-sm font-medium">Label Group</label>
                    <select
                        id="label-group"
                        class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                        bind:value={newLabel.label_group}
                    >
                        <option value="" disabled selected>Select a group</option>
                        {#each groupOptions as group}
                            <option value={group.key}>{group.value}</option>
                        {/each}
                    </select>
                </div>
                <div>
                    <label for="label-color" class="mb-2 block text-sm font-medium">Label Color</label>
                    <div class="flex items-center gap-2">
                        <input
                            id="label-color"
                            type="color"
                            class="h-10 w-10 rounded border border-gray-400"
                            bind:value={newLabel.label_color}
                        />
                        <input
                            type="text"
                            class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                            bind:value={newLabel.label_color}
                        />
                    </div>
                </div>
                <div>
                    <label for="label-order" class="mb-2 block text-sm font-medium">Label Order</label>
                    <input
                        id="label-order"
                        type="number"
                        min="0"
                        class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                        bind:value={newLabel.label_order}
                    />
                </div>
            </div>
            <Dialog.Footer>
                <Dialog.Close class="btn-secondary btn-sm w-fit">Cancel</Dialog.Close>
                <button class="btn-primary btn-sm w-fit" onclick={createLabel}>Create Label</button>
            </Dialog.Footer>
        </Dialog.Content>
    </Dialog.Root>
</div>

<!-- Edit Label Dialog -->
<Dialog.Root bind:open={editDialogOpen}>
    <Dialog.Content>
        <Dialog.Header>
            <h3 class="text-xl font-semibold">Edit Label</h3>
        </Dialog.Header>
        {#if editLabel}
            <div class="flex flex-col gap-4 py-4">
                <div>
                    <label for="edit-label-name" class="mb-2 block text-sm font-medium">Label Name</label>
                    <input
                        id="edit-label-name"
                        type="text"
                        class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                        bind:value={editLabel.label_name}
                    />
                </div>
                <div>
                    <label for="edit-label-group" class="mb-2 block text-sm font-medium">Label Group</label>
                    <select
                        id="edit-label-group"
                        class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                        bind:value={editLabel.label_group}
                    >
                        <option value="" disabled>Select a group</option>
                        {#each groupOptions as group}
                            <option value={group.key}>{group.value}</option>
                        {/each}
                    </select>
                </div>
                <div>
                    <label for="edit-label-color" class="mb-2 block text-sm font-medium">Label Color</label>
                    <div class="flex items-center gap-2">
                        <input
                            id="edit-label-color"
                            type="color"
                            class="h-10 w-10 rounded border border-gray-400"
                            bind:value={editLabel.label_color}
                        />
                        <input
                            type="text"
                            class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                            bind:value={editLabel.label_color}
                        />
                    </div>
                </div>
                <div>
                    <label for="edit-label-order" class="mb-2 block text-sm font-medium">Label Order</label>
                    <input
                        id="edit-label-order"
                        type="number"
                        min="0"
                        class="w-full rounded border border-gray-400 bg-white p-2 font-normal hover:border-gray-600 focus:border-gray-600 focus:outline-none"
                        bind:value={editLabel.label_order}
                    />
                </div>
            </div>
            <Dialog.Footer>
                <button
                    class="btn-secondary btn-sm w-fit"
                    onclick={() => {
                        editLabel = null;
                        editDialogOpen = false;
                    }}
                >
                    Cancel
                </button>
                <button class="btn-primary btn-sm w-fit" onclick={updateLabel}>Update Label</button>
            </Dialog.Footer>
        {/if}
    </Dialog.Content>
</Dialog.Root>

<!-- Labels Table -->
<div class="relative overflow-hidden rounded-lg bg-white ring-4 ring-white/25">
    {#if isLoading}
        <div class="flex h-40 items-center justify-center">
            <div class="flex items-center justify-center gap-2">
                <div class="h-8 w-8 animate-spin rounded-full border-2 border-gray-200 border-t-gray-400"></div>
                <span class="font-semibold">Loading...</span>
            </div>
        </div>
    {:else}
        {#each groupOptions as group}
            {#if labelsByGroup[group.key] && labelsByGroup[group.key].length > 0}
                <div class="border-b border-gray-300 p-4 last:border-b-0">
                    <h3 class="mb-4 text-lg font-semibold">{group.value}</h3>
                    <div class="grid gap-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                        {#each labelsByGroup[group.key] as label}
                            <div class="flex items-center justify-between rounded border border-gray-300 p-3">
                                <div class="flex items-center gap-2">
                                    <div
                                        class="h-4 w-4 rounded-full"
                                        style="background-color: {label.label_color};"
                                    ></div>
                                    <span>{label.label_name}</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <button
                                        onclick={() => openEditDialog(label)}
                                        class="rounded-full border border-gray-400 bg-white p-2"
                                        aria-label="Edit"
                                    >
                                        <svg class="h-3 w-3 fill-black">
                                            <use href="#icon-edit"></use>
                                        </svg>
                                    </button>
                                    <button
                                        onclick={() => deleteLabel(label)}
                                        class="rounded-full border border-gray-400 bg-white p-2"
                                        aria-label="Delete"
                                    >
                                        <svg class="h-3 w-3 stroke-current">
                                            <use href="#icon-delete"></use>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        {/each}
                    </div>
                </div>
            {/if}
        {/each}

        {#if Object.keys(labelsByGroup).length === 0}
            <div class="p-4 text-center">
                <p>No labels found. Create your first label to get started.</p>
            </div>
        {/if}
    {/if}
</div>
