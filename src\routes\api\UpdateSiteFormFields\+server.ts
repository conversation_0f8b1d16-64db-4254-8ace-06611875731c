import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { saveSiteJSON } from '$lib/services/sites';
import type { BaseResponse } from '$lib/types/responses';

export interface UpdateSiteFormFieldsRequest {
    site_id: string;
    form_fields: string;
}

export interface UpdateSiteFormFieldsResponse extends BaseResponse {
    // No additional properties
}

export const POST: RequestHandler = async ({ request }) => {
    try {
        const body = await request.json() as UpdateSiteFormFieldsRequest;
        const { site_id, form_fields } = body;

        if (!site_id) {
            return json({
                status: 'error',
                message: 'Site ID is required'
            } as UpdateSiteFormFieldsResponse, { status: 400 });
        }

        if (!form_fields) {
            return json({
                status: 'error',
                message: 'Form fields data is required'
            } as UpdateSiteFormFieldsResponse, { status: 400 });
        }

        try {
            JSON.parse(form_fields);
        } catch (error) {
            return json({
                status: 'error',
                message: 'Invalid JSON format for form fields'
            } as UpdateSiteFormFieldsResponse, { status: 400 });
        }

        await saveSiteJSON(site_id, form_fields);

        return json({
            status: 'success',
            message: 'Site form fields updated successfully'
        } as UpdateSiteFormFieldsResponse, { status: 200 });
    } catch (error) {
        console.error('Unexpected error at UpdateSiteFormFields:', error);
        return json({
            status: 'error',
            message: `Unexpected error at UpdateSiteFormFields: ${error instanceof Error ? error.message : 'Unknown error'}`
        } as UpdateSiteFormFieldsResponse, { status: 500 });
    }
}
