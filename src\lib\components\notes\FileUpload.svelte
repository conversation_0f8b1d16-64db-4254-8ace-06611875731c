<script lang="ts">
    import { toast } from 'svelte-sonner';
    import type { UploadFileResponse, DeleteFileResponse } from '$lib/types/responses';
    import type { NoteFile } from '$lib/types/tables';

    let {
        files = $bindable([]),
        disabled = false
    }: {
        files: NoteFile[];
        disabled?: boolean;
    } = $props();

    let isUploading = $state(false);
    let fileInput = $state<HTMLInputElement>();
    let isDragOver = $state(false);

    function handleFileSelect(event: Event): void {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];
        if (file) {
            isUploading = true;
            uploadFile(file);
            if (fileInput) fileInput.value = '';
        }
    }

    function handleDragOver(event: DragEvent): void {
        event.preventDefault();
        event.stopPropagation();
        if (!disabled && !isUploading) {
            isDragOver = true;
        }
    }

    function handleDragEnter(event: DragEvent): void {
        event.preventDefault();
        event.stopPropagation();
        if (!disabled && !isUploading) {
            isDragOver = true;
        }
    }

    function handleDragLeave(event: DragEvent): void {
        event.preventDefault();
        event.stopPropagation();
        // Only set isDragOver to false if we're leaving the drop zone entirely
        const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
        const x = event.clientX;
        const y = event.clientY;

        if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
            isDragOver = false;
        }
    }

    function handleDrop(event: DragEvent): void {
        event.preventDefault();
        event.stopPropagation();
        isDragOver = false;

        if (disabled || isUploading) return;

        const files = event.dataTransfer?.files;
        if (files && files.length > 0) {
            const file = files[0];
            isUploading = true;
            uploadFile(file);
        }
    }

    function handleDropAreaClick(): void {
        if (!disabled && !isUploading && fileInput) {
            fileInput.click();
        }
    }

    function handleKeyDown(event: KeyboardEvent): void {
        if ((event.key === 'Enter' || event.key === ' ') && !disabled && !isUploading) {
            event.preventDefault();
            handleDropAreaClick();
        }
    }

    async function uploadFile(file: File): Promise<void> {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/UploadFile', {
                method: 'POST',
                body: formData
            });

            const data = await response.json() as UploadFileResponse;

            if (data.status === 'success') {
                const newFile: NoteFile = {
                    file_url: data.file_url,
                    file_name: data.file_name,
                    file_size: data.file_size,
                    file_type: data.file_type
                };
                files = [...files, newFile];
                toast.success('File uploaded successfully');
            } else {
                toast.error(data.message || 'Failed to upload file');
            }
        } catch (error) {
            console.error('Error uploading file:', error);
            toast.error('Failed to upload file');
        } finally {
            isUploading = false;
        }
    }

    async function removeFile(index: number): Promise<void> {
        const fileToRemove = files[index];
        if (!fileToRemove) return;

        try {
            // Call the DeleteFile API to remove the file from the server
            const response = await fetch('/api/DeleteFile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    file_url: fileToRemove.file_url
                })
            });

            const data = await response.json() as DeleteFileResponse;

            if (data.status === 'success') {
                // Remove from the files array only if server deletion was successful
                files = files.filter((_, i) => i !== index);
                toast.success('File removed successfully');
            } else {
                toast.error(data.message || 'Failed to remove file');
            }
        } catch (error) {
            console.error('Error removing file:', error);
            toast.error('Failed to remove file');
        }
    }

    function formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function getFileIcon(fileType: string): string {
        if (fileType.startsWith('image/')) return '🖼️';
        if (fileType.includes('pdf')) return '📄';
        if (fileType.includes('word') || fileType.includes('document')) return '📝';
        if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊';
        if (fileType.includes('powerpoint') || fileType.includes('presentation')) return '📊';
        if (fileType.includes('zip') || fileType.includes('compressed')) return '🗜️';
        if (fileType.startsWith('text/')) return '📄';
        return '📎';
    }
</script>

<div class="grid gap-4">
    <div class="grid gap-2">
        <label for="file-upload" class="text-sm font-medium">File Attachments</label>

        <!-- Upload Area -->
        <div class="relative">
            <div
                class="flex justify-center rounded-lg border border-dashed px-6 py-10 transition-colors cursor-pointer {isDragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300'}"
                role="button"
                tabindex="0"
                aria-label="File upload area - click to select files or drag and drop files here"
                ondragover={handleDragOver}
                ondragenter={handleDragEnter}
                ondragleave={handleDragLeave}
                ondrop={handleDrop}
                onclick={handleDropAreaClick}
                onkeydown={handleKeyDown}
            >
                <div class="text-center">
                    {#if isUploading}
                        <div class="flex items-center justify-center gap-2">
                            <div class="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600"></div>
                            <span class="text-sm text-gray-600">Uploading...</span>
                        </div>
                    {:else}
                        <svg class="mx-auto h-12 w-12 {isDragOver ? 'text-blue-400' : 'text-gray-300'}" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="mt-4">
                            <label for="file-upload" class="cursor-pointer">
                                <span class="mt-2 block text-sm font-semibold {isDragOver ? 'text-blue-600' : 'text-gray-900'}">
                                    {isDragOver ? 'Drop file here' : 'Upload a file'}
                                </span>
                                <span class="mt-1 block text-xs {isDragOver ? 'text-blue-500' : 'text-gray-500'}">
                                    {isDragOver ? 'Release to upload' : 'Click to upload or drag and drop'}
                                </span>
                                <span class="mt-1 block text-xs text-gray-500">PDF, Word, Excel, Images, ZIP up to 10MB</span>
                            </label>
                            <input
                                id="file-upload"
                                type="file"
                                class="sr-only"
                                onchange={handleFileSelect}
                                bind:this={fileInput}
                                {disabled}
                                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.jpg,.jpeg,.png,.gif,.webp,.zip"
                            />
                        </div>
                    {/if}
                </div>
            </div>
        </div>

        <!-- File List -->
        {#if files.length > 0}
            <div class="mt-4 space-y-2">
                <h4 class="text-sm font-medium text-gray-900">Attached Files ({files.length})</h4>
                {#each files as file, index}
                    <div class="flex items-center justify-between rounded-md border border-gray-200 p-3">
                        <div class="flex items-center space-x-3">
                            <span class="text-lg">{getFileIcon(file.file_type)}</span>
                            <div>
                                <p class="text-sm font-medium text-gray-900">{file.file_name}</p>
                                <p class="text-xs text-gray-500">{formatFileSize(file.file_size)}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <a
                                href={file.file_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                class="text-blue-600 hover:text-blue-800 text-sm"
                            >
                                Download
                            </a>
                            {#if !disabled}
                                <button
                                    type="button"
                                    onclick={() => removeFile(index)}
                                    class="text-red-600 hover:text-red-800 text-sm"
                                >
                                    Remove
                                </button>
                            {/if}
                        </div>
                    </div>
                {/each}
            </div>
        {/if}
    </div>
</div>
