import { json } from '@sveltejs/kit';
import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { getSiteJSON } from '$lib/services/sites';
import { isValidURL } from '$lib/utils/validation';
import type { GetSiteJSONRequest } from '$lib/types/requests';
import type { GetSiteJSONResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const req = await request.json() as GetSiteJSONRequest;

        if (!req.url) {
            return json({
                status: 'error',
                message: 'Site submit URL is required'
            } as GetSiteJSONResponse, { status: 400 });
        }

        if (!isValidURL(req.url)) {
            return json({
                status: 'error',
                message: 'Invalid site submit URL format'
            } as GetSiteJSONResponse, { status: 400 });
        }

        const result = await getSiteJSON(req.url);

        if (!result) {
            return json({
                status: 'error',
                message: 'Site not found'
            } as GetSiteJSONResponse, { status: 404 });
        }

        return json({
            status: 'success',
            message: 'Site JSON retrieved successfully',
            site_id: result.site_id,
            site_url: result.site_url,
            json_data: result.json_data
        } as GetSiteJSONResponse, { status: 200 });

    } catch (error) {
        console.error('Unexpected error at GetSiteJSON:', error);
        return json({
            status: 'error',
            message: `Unexpected error at GetSiteJSON: ${error instanceof Error ? error.message : 'Unknown error'}`
        } as GetSiteJSONResponse, { status: 500 });
    }
}