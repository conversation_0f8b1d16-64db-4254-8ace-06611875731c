import { json } from '@sveltejs/kit';
import { saveLabel } from '$lib/services/labels';
import type { <PERSON>questHandler } from './$types';
import type { SaveLabelRequest } from '$lib/types/requests';
import type { SaveLabelResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const labelRequest = await request.json() as SaveLabelRequest;

        if (!labelRequest.label_name) {
            return json({
                status: "error",
                message: "Label name is required"
            } as SaveLabelResponse, { status: 400 });
        }

        if (!labelRequest.label_group) {
            return json({
                status: "error",
                message: "Label group is required"
            } as SaveLabelResponse, { status: 400 });
        }

        const label = await saveLabel(
            labelRequest.label_id,
            labelRequest.label_name,
            labelRequest.label_group,
            labelRequest.label_color,
            labelRequest.label_order
        );

        const isUpdate = !!labelRequest.label_id;
        const message = isUpdate ? "Label updated successfully" : "Label created successfully";

        return json({
            status: "success",
            message,
            label
        } as SaveLabelResponse, { status: isUpdate ? 200 : 201 });
    } catch (error) {
        console.error('Unexpected error at SaveLabel:', error);
        return json({
            status: "error",
            message: `Unexpected error at SaveLabel: ${error instanceof Error ? error.message : 'Unknown error'}`
        } as SaveLabelResponse, { status: 500 });
    }
}
