import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { saveSiteDR } from '$lib/services/extensions';
import { isValidURL } from '$lib/utils/validation';
import type { SaveDRRequest } from '$lib/types/requests';
import type { SaveDRResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
  try {
        const req = await request.json() as SaveDRRequest;
        const { dr, url } = req;

        if (!dr || !url) {
            return json({
                status: 'error',
                message: 'DR and URL are required'
            } as SaveDRResponse, { status: 400 });
        }

        if (!isValidURL(url)) {
            return json({
                status: 'error',
                message: 'Invalid URL format'
            } as SaveDRResponse, { status: 400 });
        }

        const drNum = parseFloat(dr);
        if (isNaN(drNum)) {
            return json({
                status: 'error',
                message: 'DR is not a number'
            } as SaveDRResponse, { status: 400 });
        }

        await saveSiteDR(drNum, url);

        return json({
            status: 'success',
            message: 'DR saved successfully'
        } as SaveDRResponse, { status: 200 });
    } catch (error) {
        console.error('Unexpected error at SaveDR:', error);
        return json({
            status: 'error',
            message: `Unexpected error at SaveDR: ${error instanceof Error ? error.message : 'Unknown error'}`
        } as SaveDRResponse, { status: 500 });
    }
}