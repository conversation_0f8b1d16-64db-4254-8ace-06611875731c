import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getFirstURL, extractDomain } from '$lib/services/extensions';
import { getLabelIdByName } from '$lib/services/labels';
import type { GetURLRequest } from '$lib/types/requests';
import type { GetURLResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
  try {
        // Default sortby value
        let sortby: 'dr' | 'visits' = 'dr';

        // Try to parse the request body, but don't fail if it's not valid JSON
        try {
            const req = await request.json() as GetURLRequest;
            // Use the provided sortby if it exists and is valid, otherwise use the default
            if (req && (req.sortby === 'dr' || req.sortby === 'visits')) {
                sortby = req.sortby;
            }
        } catch (e) {
            // If JSON parsing fails, we'll use the default sortby value
            console.log('No valid JSON in request, using default sortby value');
        }

        // Get the approved and not approved labels
        const approvedId = await getLabelIdByName('Approved');
        const notApprovedId = await getLabelIdByName('Not Approved');

        // Create an array of label IDs to filter by
        const filterLabelIds: string[] = [];

        // Add the approved and not approved IDs if they exist
        if (approvedId) filterLabelIds.push(approvedId);
        if (notApprovedId) filterLabelIds.push(notApprovedId);

        // Get the first URL with the specified labels
        const firstURL = await getFirstURL(sortby, filterLabelIds.length > 0 ? filterLabelIds : undefined);
        const url = extractDomain(firstURL);

        return json({
            status: 'success',
            message: 'URL retrieved successfully',
            url
        } as GetURLResponse, { status: 200 });
    } catch (error) {
        console.error('Unexpected error at GetURL:', error);
        return json({
            status: 'error',
            message: `Unexpected error at GetURL: ${error instanceof Error ? error.message : 'Unknown error'}`
        } as GetURLResponse, { status: 500 });
    }
}