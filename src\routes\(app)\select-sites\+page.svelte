<script lang="ts">
    import type { SiteWithLabelIds } from '$lib/types/services';
    import type { Label } from '$lib/types/tables';
    import type { GetSitesRequest } from '$lib/types/requests';
    import type { GetLabelsResponse, GetSitesResponse } from '$lib/types/responses';
    import { onMount } from 'svelte';
    import { getLabelByName } from '$lib/utils/labels';
    import Head from "$lib/components/layout/Head.svelte";
    import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
    import { formatVisits } from '$lib/utils/format';

    let sites = $state<SiteWithLabelIds[]>([]);
    let labels = $state<Label[]>([]);
    let loading = $state<boolean>(true);
    let error = $state<string | null>(null);
    let selectedSiteIds = $state<string[]>([]);
    let selectAll = $state<boolean>(false);

    let checkedCount = $derived.by(() => {
        return sites.filter((site: SiteWithLabelIds) => selectedSiteIds.includes(site.site_id)).length;
    });

    onMount(async () => {
        try {
            loading = true;

            // Load labels first to get the Approved label ID
            const labelsResponse = await fetch('/api/GetLabels', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const labelsData = await labelsResponse.json() as GetLabelsResponse;

            if (labelsData.status === 'success') {
                labels = labelsData.labels;
            } else {
                throw new Error(labelsData.message || 'Failed to fetch labels');
            }

            // Find the Approved label ID
            const approvedLabel = getLabelByName('Approved', labels);
            const approvedLabelId = approvedLabel ? approvedLabel.label_id : null;

            // Load sites with the Approved label filter if found
            const sitesRequest: GetSitesRequest = {
                orLabelIds: approvedLabelId ? [approvedLabelId] : [],
                page_num: 1,
                per_page: 500,
                sort: 'dr'
            };

            const sitesResponse = await fetch('/api/GetSites', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(sitesRequest)
            });

            const sitesData = await sitesResponse.json() as GetSitesResponse;

            if (sitesData.status === 'success') {
                sites = sitesData.sites;
            } else {
                throw new Error(sitesData.message || 'Failed to fetch sites');
            }

            loading = false;
        } catch (err) {
            console.error('Error loading data:', err);
            error = 'Failed to load data. Please try again.';
            loading = false;
        }
    });

    function handleSelectAll() {
        if (selectAll) {
            selectedSiteIds = sites.map((site: SiteWithLabelIds) => site.site_id);
        } else {
            selectedSiteIds = [];
        }
    }

    function handleSiteSelect(siteId: string) {
        const index = selectedSiteIds.indexOf(siteId);
        if (index === -1) {
            selectedSiteIds = [...selectedSiteIds, siteId];
        } else {
            selectedSiteIds = selectedSiteIds.filter(id => id !== siteId);
        }

        selectAll = sites.length > 0 && selectedSiteIds.length === sites.length;
    }

    function saveSites() {
        if (selectedSiteIds.length === 0) {
            alert('Please select at least one site.');
            return;
        }

        console.log('Selected site IDs:', selectedSiteIds);
        alert(`Selected ${selectedSiteIds.length} sites`);

        // Here you can implement what happens when sites are selected
        // For example, redirect to another page or make an API call
    }

    function getLabelsForGroup(site: SiteWithLabelIds, group: string) {
        return labels.filter((label: Label) =>
            label.label_group === group &&
            site.label_ids.includes(label.label_id)
        );
    }
</script>

<Head
    title={`Select Sites - ${PUBLIC_SITE_NAME}`}
    description="Select sites for your SaaS directory submissions"
    url={`${PUBLIC_SITE_URL}/select-sites`}
/>

{#if loading}
    <section class="bg-secondary py-8 md:py-24">
        <div class="container">
            <div class="mb-12 flex items-start justify-between gap-2 md:gap-4">
                <h1 class="text-3xl font-bold text-white md:text-3xl lg:text-4xl mb-0">Select Sites</h1>
            </div>
            <div class="bg-white rounded-lg">
                <div class="flex flex-col relative">
                    <div class="flex flex-col items-start justify-between gap-4 p-4 border-b border-gray-300 lg:items-center lg:flex-row">
                        <div class="w-full">
                            <h3 class="text-lg font-semibold">Loading...</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{:else if error}
    <section class="bg-secondary py-8 md:py-24">
        <div class="container">
            <div class="mb-12 flex items-start justify-between gap-2 md:gap-4">
                <h1 class="text-3xl font-bold text-white md:text-3xl lg:text-4xl mb-0">Select Sites</h1>
            </div>
            <div class="bg-white rounded-lg">
                <div class="flex flex-col relative">
                    <div class="flex flex-col items-start justify-between gap-4 p-4 border-b border-gray-300 lg:items-center lg:flex-row">
                        <div class="w-full">
                            <h3 class="text-lg font-semibold">{error}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{:else}
    <section class="bg-secondary py-8 md:py-24">
        <div class="container">
            <div class="mb-12 flex items-start justify-between gap-2 md:gap-4">
                <h1 class="text-3xl font-bold text-white md:text-3xl lg:text-4xl mb-0">Select Sites</h1>
                <button class="btn-primary w-fit" onclick={saveSites}>Save Sites</button>
            </div>
            <div class="bg-white rounded-lg">
                <div class="flex flex-col relative">
                    <div class="flex flex-col items-start justify-between gap-4 p-4 border-b border-gray-300 lg:items-center lg:flex-row">
                        <div class="w-full">
                            <h3 class="text-lg font-semibold">Select the sites you want to include by checking the boxes</h3>
                        </div>
                    </div>
                    <div id="select-sites-table" class="flex flex-col">
                        <div class="overflow-x-auto flex-1">
                            <div class="overflow-auto">
                                <table class="table-fixed w-full text-sm text-left text-gray-500 font-semibold">
                                    <thead class="text-xs text-gray-600 uppercase bg-white">
                                        <tr class="border-b border-gray-300">
                                            <th scope="col" class="px-4 py-4 w-[50px] whitespace-nowrap">
                                                <input
                                                    type="checkbox"
                                                    id="select-all"
                                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                                    checked={selectAll}
                                                    onclick={() => { selectAll = !selectAll; handleSelectAll(); }}
                                                >
                                            </th>
                                            <th scope="col" class="px-4 py-4 w-[70px] whitespace-nowrap">#</th>
                                            <th scope="col" class="px-4 py-4 w-[260px] whitespace-nowrap">Name</th>
                                            <th scope="col" class="px-4 py-4 w-[100px] whitespace-nowrap">DR</th>
                                            <th scope="col" class="px-4 py-4 w-[100px] whitespace-nowrap">Visits</th>
                                            <th scope="col" class="px-4 py-4 w-[170px] whitespace-nowrap">Type</th>
                                            <th scope="col" class="px-4 py-4 w-[160px] whitespace-nowrap">Pricing</th>
                                            <th scope="col" class="px-4 py-4 w-[160px] whitespace-nowrap">Link Type</th>
                                            <th scope="col" class="px-4 py-4 w-[130px] whitespace-nowrap">URL</th>
                                            <th scope="col" class="px-4 py-4 w-[130px] whitespace-nowrap">Submit URL</th>
                                        </tr>
                                    </thead>
                                    <tbody class="text-sm text-black">
                                        {#each sites as site, i}
                                            <tr class="border-b border-gray-300 hover:bg-gray-200">
                                                <td class="w-[50px] px-4 py-2">
                                                    <input
                                                        type="checkbox"
                                                        id={`site-${site.site_id}`}
                                                        value={site.site_id}
                                                        class="site-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                                        checked={selectedSiteIds.includes(site.site_id)}
                                                        onclick={() => handleSiteSelect(site.site_id)}
                                                    >
                                                </td>
                                                <td class="w-[70px] px-4 py-2">{i + 1}.</td>
                                                <td class="w-[260px] px-4 py-2 truncate whitespace-nowrap">{site.site_name}</td>
                                                <td class="w-[100px] px-4 py-2 text-sm">
                                                    {site.site_dr || '-'}
                                                </td>
                                                <td class="w-[100px] px-4 py-2 text-sm">
                                                    {formatVisits(site.site_visits)}
                                                </td>

                                                <td class="w-[170px] px-4 py-2">
                                                    {#each getLabelsForGroup(site, 'type') as label}
                                                        <div
                                                            style={`background:${label.label_color}1a;border-color:${label.label_color};color:${label.label_color}`}
                                                            class="rounded-2xl font-semibold border text-xs px-3 py-1 w-fit whitespace-nowrap"
                                                        >
                                                            {label.label_name}
                                                        </div>
                                                    {/each}
                                                </td>

                                                <td class="w-[160px] px-4 py-2">
                                                    {#each getLabelsForGroup(site, 'pricing') as label}
                                                        <div
                                                            style={`background:${label.label_color}1a;border-color:${label.label_color};color:${label.label_color}`}
                                                            class="rounded-2xl font-semibold border text-xs px-3 py-1 w-fit whitespace-nowrap"
                                                        >
                                                            {label.label_name}
                                                        </div>
                                                    {/each}
                                                </td>

                                                <td class="w-[160px] px-4 py-2">
                                                    {#each getLabelsForGroup(site, 'link-type') as label}
                                                        <div
                                                            style={`background:${label.label_color}1a;border-color:${label.label_color};color:${label.label_color}`}
                                                            class="rounded-2xl font-semibold border text-xs px-3 py-1 w-fit whitespace-nowrap"
                                                        >
                                                            {label.label_name}
                                                        </div>
                                                    {/each}
                                                </td>

                                                <td class="w-[130px] px-4 py-2">
                                                    <a rel="nofollow" target="_blank" href={site.site_url} class="flex items-center justify-center text-sm font-semibold text-center text-black hover:text-secondary hover:underline gap-2 w-fit">
                                                        Visit
                                                        <svg class="w-3 h-3 fill-current"><use href="#icon-external"></use></svg>
                                                    </a>
                                                </td>

                                                <td class="w-[130px] px-4 py-2">
                                                    <a rel="nofollow" target="_blank" href={site.site_submit_url} class="flex items-center justify-center text-sm font-semibold text-center text-black hover:text-secondary hover:underline gap-2 w-fit">
                                                        Submit
                                                        <svg class="w-3 h-3 fill-current"><use href="#icon-external"></use></svg>
                                                    </a>
                                                </td>
                                            </tr>
                                        {/each}
                                    </tbody>
                                </table>
                                <div class="flex flex-col items-start justify-between mt-[-1px] p-4 space-y-3 border-t border-gray-300 md:flex-row md:items-center md:space-y-0">
                                    <button class="btn-primary w-fit" onclick={saveSites}>
                                        Save Sites ({checkedCount} selected)
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{/if}