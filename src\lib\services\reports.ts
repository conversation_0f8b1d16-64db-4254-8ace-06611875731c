import sql from '$lib/db/db';
import type { Report } from '$lib/types/tables';
import { generateUUID, isValidUUID } from '$lib/utils/uuid';
import { generateReportPassword } from '$lib/utils/generate';

export async function getReports(): Promise<Report[]> {
    const reports = await sql<Report[]>`
        SELECT
            report_id, report_website_url, report_email_address, report_email_password,
            report_created_at, report_status, report_password
        FROM reports
        ORDER BY report_created_at DESC
    `;

    return reports;
}

export async function getReport(reportId: string): Promise<Report> {
    const reports = await sql<Report[]>`
        SELECT
            report_id, report_website_url, report_email_address, report_email_password,
            report_created_at, report_status, report_password
        FROM reports
        WHERE report_id = ${reportId}
    `;

    if (reports.length === 0) {
        throw new Error(`No report found with id ${reportId}`);
    }

    return reports[0];
}

export async function reportExists(reportId: string): Promise<Report | null> {
    // Check if it's a valid UUID
    if (isValidUUID(reportId)) {
        try {
            // Try to find by ID
            const resultById = await sql<Report[]>`
                SELECT
                    report_id, report_website_url, report_email_address, report_email_password,
                    report_created_at, report_status, report_password
                FROM reports
                WHERE report_id = ${reportId}
            `;

            if (resultById.length > 0) {
                return resultById[0];
            }
        } catch (error) {
            console.error('Error finding report by UUID:', error);
        }
    }

    return null;
}

export async function saveReport(report_id: string | undefined, report_website_url: string, report_email_address: string, report_email_password: string, report_status?: number, report_password?: string): Promise<Report> {
    const currentTime = Math.floor(Date.now() / 1000);
    const id = report_id || generateUUID();
    const isNewReport = !report_id;

    // For new reports: use provided password or generate one if none provided
    // For existing reports: use provided password or keep existing one
    const password = isNewReport
        ? (report_password && report_password.trim() ? report_password : generateReportPassword())
        : (report_password || '');

    if (isNewReport) {
        // Create new report with custom or generated password
        const result = await sql<Report[]>`
            INSERT INTO reports (report_id, report_website_url, report_email_address, report_email_password, report_created_at, report_status, report_password)
            VALUES (${id}, ${report_website_url}, ${report_email_address}, ${report_email_password}, ${currentTime}, ${report_status || 0}, ${password})
            RETURNING *
        `;
        return result[0];
    } else {
        // Update existing report including password if provided
        const result = await sql<Report[]>`
            UPDATE reports SET
                report_website_url = ${report_website_url},
                report_email_address = ${report_email_address},
                report_email_password = ${report_email_password},
                report_status = ${report_status || 0},
                report_password = ${password}
            WHERE report_id = ${id}
            RETURNING *
        `;
        return result[0];
    }
}

export async function deleteReport(reportId: string): Promise<void> {
    const result = await sql`
        DELETE FROM reports
        WHERE report_id = ${reportId}
        RETURNING report_id
    `;

    if (result.length === 0) {
        throw new Error('No report found with the given ID');
    }
}

export function getReportStatusLabel(status: number): string {
    switch (status) {
        case 0:
            return 'In Progress';
        case 1:
            return 'Completed';
        case 2:
            return 'Sent';
        default:
            return 'Unknown';
    }
}

export async function updateReportStatus(reportId: string, status: number): Promise<Report> {
    const result = await sql<Report[]>`
        UPDATE reports
        SET report_status = ${status}
        WHERE report_id = ${reportId}
        RETURNING *
    `;

    if (result.length === 0) {
        throw new Error('No report found with the given ID');
    }

    return result[0];
}

