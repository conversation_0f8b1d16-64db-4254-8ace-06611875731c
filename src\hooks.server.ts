import { verifyToken } from '$lib/services/auth';
import type { Handle } from '@sveltejs/kit';
import type { JwtPayload } from '$lib/services/auth';

// Extend the App.Locals interface to include user property
declare global {
  namespace App {
    interface Locals {
      user?: JwtPayload;
    }
  }
}

export const handle: Handle = async ({ event, resolve }) => {
  // Get the token from cookies
  const token = event.cookies.get('access_token');

  // Set user in locals if token exists and is valid
  if (token) {
    const user = verifyToken(token);
    if (user) {
      event.locals.user = user;
    }
  }

  // Process the request
  const response = await resolve(event);

  // Add CORS headers for all routes
  // Get the origin from the request
  const origin = event.request.headers.get('origin') || '*';

  // Set CORS headers to allow all origins and methods
  response.headers.set('Access-Control-Allow-Origin', origin);
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Max-Age', '3600');

  // Handle preflight requests
  if (event.request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: response.headers
    });
  }

  return response;
};
