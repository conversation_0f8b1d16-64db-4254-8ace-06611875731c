<script lang="ts">
    import type { Note, NoteFile } from '$lib/types/tables';
    import { parseNoteFiles } from '$lib/utils/fileUtils';

    interface Props {
        note: Note;
        onEdit: (note: Note) => void;
        onDelete: (noteId: string) => void;
        onDragStart?: (note: Note) => void;
    }

    let { note, onEdit, onDelete, onDragStart }: Props = $props();

    let isDragging = $state(false);

    function formatDate(timestamp: number): string {
        return new Date(timestamp * 1000).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    function getFiles(): NoteFile[] {
        return parseNoteFiles(note.note_files);
    }

    function truncateContent(content: string, maxLength: number = 100): string {
        if (content.length <= maxLength) return content;
        return content.substring(0, maxLength) + '...';
    }

    function handleDragStart(event: DragEvent) {
        if (event.dataTransfer) {
            event.dataTransfer.setData('text/plain', note.note_id);
            event.dataTransfer.setData('application/json', JSON.stringify(note));
            event.dataTransfer.effectAllowed = 'move';
        }
        isDragging = true;
        onDragStart?.(note);
    }

    function handleDragEnd() {
        isDragging = false;
    }
</script>

<div
    role="button"
    tabindex="0"
    draggable="true"
    ondragstart={handleDragStart}
    ondragend={handleDragEnd}
    class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-grab hover:shadow-md transition-shadow {isDragging ? 'opacity-50 rotate-2' : ''}"
    aria-label="Draggable note card: {note.note_title}"
>
    <div class="flex items-start justify-between mb-3">
        <h3 class="text-sm font-semibold text-gray-900 line-clamp-2 flex-1 mr-2">
            {note.note_title}
        </h3>
        <div class="flex items-center gap-1 flex-shrink-0">
            <button
                onclick={() => onEdit(note)}
                class="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                title="Edit note"
                aria-label="Edit note"
            >
                <svg class="h-4 w-4 fill-current">
                    <use href="#icon-edit"></use>
                </svg>
            </button>
            <button
                onclick={() => onDelete(note.note_id)}
                class="p-1 text-gray-400 hover:text-red-600 transition-colors"
                title="Delete note"
                aria-label="Delete note"
            >
                <svg class="h-4 w-4 fill-current">
                    <use href="#icon-trash"></use>
                </svg>
            </button>
        </div>
    </div>

    {#if note.note_content}
        <p class="text-xs text-gray-600 mb-3 line-clamp-3">
            {truncateContent(note.note_content)}
        </p>
    {/if}

    {#if getFiles().length > 0}
        <div class="flex items-center gap-1 mb-3">
            <svg class="h-3 w-3 fill-gray-400">
                <use href="#icon-paperclip"></use>
            </svg>
            <span class="text-xs text-gray-500">{getFiles().length} file{getFiles().length !== 1 ? 's' : ''}</span>
        </div>
    {/if}

    <div class="flex items-center justify-between text-xs text-gray-500">
        <span>{formatDate(note.note_created_at)}</span>
    </div>
</div>

<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
