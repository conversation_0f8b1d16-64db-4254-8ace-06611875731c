import type { Label } from "$lib/types/tables";

export function labelExists(label_id: string, labels: Label[]): boolean {
    return labels.some(label => label.label_id === label_id);
}

export function getLabelbyId(label_id: string, labels: Label[]): Label | undefined {
    return labels.find(label => label.label_id === label_id);
}

export function getLabelByName(name: string, labels: Label[]): Label | undefined {
    return labels.find(label => label.label_name === name);
}

/**
 * Sort labels by label_order in descending order (higher values first)
 * @param labels Array of labels to sort
 * @returns Sorted array of labels
 */
export function sortLabelsByOrder(labels: Label[]): Label[] {
    return [...labels].sort((a, b) => {
        // Default to 0 if label_order is undefined
        const orderA = a.label_order ?? 0;
        const orderB = b.label_order ?? 0;

        // Sort by label_order in descending order (higher values first)
        if (orderA !== orderB) {
            return orderB - orderA;
        }

        // If label_order is the same, sort by name as a fallback
        return a.label_name.localeCompare(b.label_name);
    });
}

/**
 * Sort label IDs by their corresponding labels' order
 * @param labelIds Array of label IDs to sort
 * @param labels Array of all available labels
 * @returns Sorted array of label IDs
 */
export function sortLabelIdsByOrder(labelIds: string[], labels: Label[]): string[] {
    return [...labelIds].sort((idA, idB) => {
        const labelA = getLabelbyId(idA, labels);
        const labelB = getLabelbyId(idB, labels);

        // Default to 0 if label or label_order is undefined
        const orderA = labelA?.label_order ?? 0;
        const orderB = labelB?.label_order ?? 0;

        // Sort by label_order in descending order (higher values first)
        if (orderA !== orderB) {
            return orderB - orderA;
        }

        // If label_order is the same, sort by name as a fallback
        return (labelA?.label_name || '').localeCompare(labelB?.label_name || '');
    });
}