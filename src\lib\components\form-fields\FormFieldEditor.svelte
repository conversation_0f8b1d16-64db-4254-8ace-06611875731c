<script lang="ts">
    import type { SiteFormField } from '$lib/types/tables';
    import CustomInputText from '$lib/components/misc/CustomInputText.svelte';
    import CustomDropdown from '$lib/components/misc/CustomDropdown.svelte';
    import * as Accordion from '$lib/components/ui/accordion';
    import { toast } from 'svelte-sonner';

    let {
        formFields = $bindable<SiteFormField[]>([]),
        onSave
    } = $props<{
        formFields: SiteFormField[];
        onSave: (fields: SiteFormField[]) => Promise<void>;
    }>();

    let editingField = $state<SiteFormField | null>(null);
    let editingIndex = $state<number>(-1);
    let showAddForm = $state(false);

    const fieldTypes = [
        { label_id: 'input-text', label_name: 'Text Input' },
        { label_id: 'input-email', label_name: 'Email Input' },
        { label_id: 'input-password', label_name: 'Password Input' },
        { label_id: 'input-number', label_name: 'Number Input' },
        { label_id: 'input-url', label_name: 'URL Input' },
        { label_id: 'input-tel', label_name: 'Phone Input' },
        { label_id: 'textarea', label_name: 'Textarea' },
        { label_id: 'select', label_name: 'Select Dropdown' },
        { label_id: 'radio-group', label_name: 'Radio Group' },
        { label_id: 'checkbox', label_name: 'Checkbox' },
        { label_id: 'file', label_name: 'File Upload' }
    ];

    function createNewField(): SiteFormField {
        return {
            type: 'input-text',
            label: '',
            match: '',
            required: false,
            selector: '',
            optionSelector: '',
            maxlength: '',
            value: '',
            defaultOption: '',
            delay: '',
            pageId: 'page-accordion-0',
            pageName: 'Page 1',
            options: []
        };
    }

    function startAddField() {
        editingField = createNewField();
        editingIndex = -1;
        showAddForm = true;
    }

    function cancelEdit() {
        editingField = null;
        editingIndex = -1;
        showAddForm = false;
    }

    function saveField() {
        if (!editingField) return;

        if (!editingField.label.trim()) {
            toast.error('Label is required');
            return;
        }

        if (!editingField.selector.trim()) {
            toast.error('Selector is required');
            return;
        }

        if (editingIndex === -1) {
            formFields = [...formFields, editingField];
        } else {
            formFields[editingIndex] = editingField;
        }

        cancelEdit();
    }

    function deleteField(index: number) {
        if (confirm('Are you sure you want to delete this field?')) {
            formFields = formFields.filter((_: any, i: number) => i !== index);
        }
    }

    function addOption() {
        if (!editingField) return;

        if (!editingField.options) {
            editingField.options = [];
        }

        editingField.options = [...editingField.options, { label: '', selector: '' }];
    }

    function removeOption(index: number) {
        if (!editingField?.options) return;
        editingField.options = editingField.options.filter((_, i) => i !== index);
    }

    function addOptionToField(fieldIndex: number) {
        if (!formFields[fieldIndex].options) {
            formFields[fieldIndex].options = [];
        }
        formFields[fieldIndex].options = [...formFields[fieldIndex].options!, { label: '', selector: '' }];
    }

    function removeOptionFromField(fieldIndex: number, optionIndex: number) {
        if (!formFields[fieldIndex].options) return;
        formFields[fieldIndex].options = formFields[fieldIndex].options!.filter((_: any, i: number) => i !== optionIndex);
    }

    async function handleSave() {
        await onSave(formFields);
    }
</script>

<div class="space-y-4">
    <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold">Form Fields ({formFields.length})</h3>
        <div class="flex gap-2">
            <button
                onclick={startAddField}
                class="btn-primary text-sm"
            >
                Add Field
            </button>
            <button
                onclick={handleSave}
                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm"
            >
                Save Changes
            </button>
        </div>
    </div>

    {#if showAddForm && editingField}
        <div class="border border-gray-300 rounded-lg p-4 bg-gray-50">
            <h4 class="font-semibold mb-4">
                {editingIndex === -1 ? 'Add New Field' : 'Edit Field'}
            </h4>

            <div class="grid gap-4 sm:grid-cols-2">
                <CustomDropdown
                    title="Field Type"
                    placeholder="Select type"
                    labels={fieldTypes}
                    bind:value={editingField.type}
                    onChange={() => {}}
                />

                <CustomInputText
                    name="field-label"
                    title="Label"
                    placeholder="Enter field label"
                    bind:value={editingField.label}
                    error=""
                    onInput={() => {}}
                />

                <CustomInputText
                    name="field-match"
                    title="Match Field"
                    placeholder="e.g., product-name, first-name"
                    bind:value={editingField.match}
                    error=""
                    onInput={() => {}}
                />

                <CustomInputText
                    name="field-selector"
                    title="CSS Selector"
                    placeholder="e.g., #email, .form-input"
                    bind:value={editingField.selector}
                    error=""
                    onInput={() => {}}
                />

                <CustomInputText
                    name="field-maxlength"
                    title="Max Length"
                    placeholder="Optional"
                    bind:value={editingField.maxlength}
                    error=""
                    onInput={() => {}}
                />

                <CustomInputText
                    name="field-value"
                    title="Default Value"
                    placeholder="Optional"
                    bind:value={editingField.value}
                    error=""
                    onInput={() => {}}
                />

                <CustomInputText
                    name="field-page-id"
                    title="Page ID"
                    placeholder="e.g., page-accordion-0"
                    bind:value={editingField.pageId}
                    error=""
                    onInput={() => {}}
                />

                <CustomInputText
                    name="field-page-name"
                    title="Page Name"
                    placeholder="e.g., Page 1"
                    bind:value={editingField.pageName}
                    error=""
                    onInput={() => {}}
                />
            </div>

            <div class="mt-4 flex items-center gap-4">
                <label class="flex items-center gap-2">
                    <input
                        type="checkbox"
                        bind:checked={editingField.required}
                        class="rounded"
                    />
                    <span class="text-sm">Required field</span>
                </label>
            </div>

            {#if editingField.type === 'radio-group' || editingField.type === 'select'}
                <div class="mt-4">
                    <div class="flex items-center justify-between mb-2">
                        <h5 class="font-medium">Options</h5>
                        <button
                            onclick={addOption}
                            class="text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded"
                        >
                            Add Option
                        </button>
                    </div>

                    {#if editingField.options && editingField.options.length > 0}
                        <div class="space-y-2">
                            {#each editingField.options as option, i}
                                <div class="flex gap-2 items-center">
                                    <input
                                        type="text"
                                        placeholder="Option label"
                                        bind:value={option.label}
                                        class="flex-1 px-3 py-2 border border-gray-300 rounded text-sm"
                                    />
                                    <input
                                        type="text"
                                        placeholder="Option selector"
                                        bind:value={option.selector}
                                        class="flex-1 px-3 py-2 border border-gray-300 rounded text-sm"
                                    />
                                    <button
                                        onclick={() => removeOption(i)}
                                        class="text-red-600 hover:text-red-800 px-2 py-1"
                                    >
                                        ×
                                    </button>
                                </div>
                            {/each}
                        </div>
                    {/if}
                </div>
            {/if}

            <div class="flex gap-2 mt-4">
                <button
                    onclick={saveField}
                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm"
                >
                    {editingIndex === -1 ? 'Add Field' : 'Update Field'}
                </button>
                <button
                    onclick={cancelEdit}
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm"
                >
                    Cancel
                </button>
            </div>
        </div>
    {/if}

    {#if formFields.length > 0}
        <Accordion.Root class="w-full" type="multiple">
            {#each formFields as field, index}
                <Accordion.Item value={`field-${index}`} class="border border-gray-200 rounded-lg mb-2">
                    <Accordion.Trigger class="flex w-full items-center justify-between p-4 text-left hover:bg-gray-50">
                        <div class="flex items-center gap-2">
                            <span class="font-medium">{field.label}</span>
                            <span class="text-xs bg-gray-100 px-2 py-1 rounded">{field.type}</span>
                            {#if field.required}
                                <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Required</span>
                            {/if}
                        </div>
                    </Accordion.Trigger>
                    <Accordion.Content class="p-4">
                        <div class="space-y-4">
                            <div class="grid gap-4 sm:grid-cols-2">
                                <CustomDropdown
                                    title="Field Type"
                                    placeholder="Select type"
                                    labels={fieldTypes}
                                    bind:value={field.type}
                                    onChange={() => {}}
                                />

                                <CustomInputText
                                    name="field-label-{index}"
                                    title="Label"
                                    placeholder="Enter field label"
                                    bind:value={field.label}
                                    error=""
                                    onInput={() => {}}
                                />

                                <CustomInputText
                                    name="field-match-{index}"
                                    title="Match Field"
                                    placeholder="e.g., product-name, first-name"
                                    bind:value={field.match}
                                    error=""
                                    onInput={() => {}}
                                />

                                <CustomInputText
                                    name="field-selector-{index}"
                                    title="CSS Selector"
                                    placeholder="e.g., #email, .form-input"
                                    bind:value={field.selector}
                                    error=""
                                    onInput={() => {}}
                                />

                                <CustomInputText
                                    name="field-maxlength-{index}"
                                    title="Max Length"
                                    placeholder="Optional"
                                    bind:value={field.maxlength}
                                    error=""
                                    onInput={() => {}}
                                />

                                <CustomInputText
                                    name="field-value-{index}"
                                    title="Default Value"
                                    placeholder="Optional"
                                    bind:value={field.value}
                                    error=""
                                    onInput={() => {}}
                                />

                                <CustomInputText
                                    name="field-page-id-{index}"
                                    title="Page ID"
                                    placeholder="e.g., page-accordion-0"
                                    bind:value={field.pageId}
                                    error=""
                                    onInput={() => {}}
                                />

                                <CustomInputText
                                    name="field-page-name-{index}"
                                    title="Page Name"
                                    placeholder="e.g., Page 1"
                                    bind:value={field.pageName}
                                    error=""
                                    onInput={() => {}}
                                />
                            </div>

                            <div class="flex items-center gap-4">
                                <label class="flex items-center gap-2">
                                    <input
                                        type="checkbox"
                                        bind:checked={field.required}
                                        class="rounded"
                                    />
                                    <span class="text-sm">Required field</span>
                                </label>
                            </div>

                            {#if field.type === 'radio-group' || field.type === 'select'}
                                <div>
                                    <div class="flex items-center justify-between mb-2">
                                        <h5 class="font-medium">Options</h5>
                                        <button
                                            onclick={() => addOptionToField(index)}
                                            class="text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded"
                                        >
                                            Add Option
                                        </button>
                                    </div>

                                    {#if field.options && field.options.length > 0}
                                        <div class="space-y-2">
                                            {#each field.options as option, optionIndex}
                                                <div class="flex gap-2 items-center">
                                                    <input
                                                        type="text"
                                                        placeholder="Option label"
                                                        bind:value={option.label}
                                                        class="flex-1 px-3 py-2 border border-gray-300 rounded text-sm"
                                                    />
                                                    <input
                                                        type="text"
                                                        placeholder="Option selector"
                                                        bind:value={option.selector}
                                                        class="flex-1 px-3 py-2 border border-gray-300 rounded text-sm"
                                                    />
                                                    <button
                                                        onclick={() => removeOptionFromField(index, optionIndex)}
                                                        class="text-red-600 hover:text-red-800 px-2 py-1"
                                                    >
                                                        ×
                                                    </button>
                                                </div>
                                            {/each}
                                        </div>
                                    {/if}
                                </div>
                            {/if}

                            <div class="flex gap-2 pt-2 border-t border-gray-200">
                                <button
                                    onclick={() => deleteField(index)}
                                    class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                                >
                                    Delete
                                </button>
                            </div>
                        </div>
                    </Accordion.Content>
                </Accordion.Item>
            {/each}
        </Accordion.Root>
    {/if}

    {#if formFields.length === 0}
        <div class="text-center py-8 text-gray-500">
            No form fields configured. Click "Add Field" to get started.
        </div>
    {/if}
</div>
