import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { PUBLIC_FILES_FOLDER } from '$lib/utils/env';
import * as fs from 'node:fs/promises';
import * as path from 'node:path';

interface DeleteFileRequest {
    file_url: string;
}

interface DeleteFileResponse {
    status: 'success' | 'error';
    message: string;
}

export const OPTIONS: RequestHandler = async () => {
    return new Response(null, { status: 204 });
};

export const POST: RequestHandler = async ({ request }) => {
    try {
        const req = await request.json() as DeleteFileRequest;

        if (!req.file_url) {
            return json({
                status: 'error',
                message: 'File URL is required'
            } as DeleteFileResponse, { status: 400 });
        }

        // Extract filename from URL
        // URL format: http://localhost:5173/files/filename.ext or https://submitsaas.com/files/filename.ext
        const urlParts = req.file_url.split('/files/');
        if (urlParts.length !== 2) {
            return json({
                status: 'error',
                message: 'Invalid file URL format'
            } as DeleteFileResponse, { status: 400 });
        }

        const fileName = urlParts[1];

        // Validate filename (should be UUID + extension)
        if (!fileName || fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
            return json({
                status: 'error',
                message: 'Invalid filename'
            } as DeleteFileResponse, { status: 400 });
        }

        const filePath = path.join(PUBLIC_FILES_FOLDER, fileName);

        try {
            // Check if file exists
            await fs.access(filePath);

            // Delete the file
            await fs.unlink(filePath);

            return json({
                status: 'success',
                message: 'File deleted successfully'
            } as DeleteFileResponse, { status: 200 });

        } catch (fileError: any) {
            if (fileError.code === 'ENOENT') {
                // File doesn't exist, consider it already deleted
                return json({
                    status: 'success',
                    message: 'File already deleted or does not exist'
                } as DeleteFileResponse, { status: 200 });
            } else {
                console.error('Error deleting file:', fileError);
                return json({
                    status: 'error',
                    message: 'Failed to delete file from server'
                } as DeleteFileResponse, { status: 500 });
            }
        }

    } catch (error) {
        console.error('Error in DeleteFile:', error);
        return json({
            status: 'error',
            message: 'An error occurred while processing your request'
        } as DeleteFileResponse, { status: 500 });
    }
};
