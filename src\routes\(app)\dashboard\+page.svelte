<script lang="ts">
    import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
    import Head from "$lib/components/layout/Head.svelte";
    import * as Tabs from '$lib/components/ui/tabs/index';
    import { onMount } from 'svelte';
    import { toast } from 'svelte-sonner';
    import type { Label } from '$lib/types/tables';
    import type { GetLabelsResponse } from '$lib/types/responses';

    import SitesTab from './SitesTab.svelte';
    import LabelsTab from './LabelsTab.svelte';

    let currentTab = $state('sites');
    let labels = $state<Label[]>([]);
    let labelsByGroup = $state<Record<string, Label[]>>({});
    let isLoading = $state(true);

    // Function to fetch labels
    async function fetchLabels(): Promise<boolean> {
        try {
            const response = await fetch('/api/GetLabels', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json() as GetLabelsResponse;

            if (data.status === 'success' && Array.isArray(data.labels)) {
                labels = data.labels;

                // Group labels by label_group
                labelsByGroup = labels.reduce((acc: Record<string, Label[]>, label: Label) => {
                    if (!acc[label.label_group]) acc[label.label_group] = [];
                    acc[label.label_group].push(label);
                    return acc;
                }, {});

                return true;
            } else {
                console.error('Invalid labels data received:', data);
                return false;
            }
        } catch (error) {
            console.error('Error fetching labels:', error);
            return false;
        } finally {
            isLoading = false;
        }
    }

    // Handle tab change
    async function handleTabChange(value: string) {
        currentTab = value;

        // If switching to the sites tab, refresh the labels
        if (value === 'sites') {
            isLoading = true;
            const success = await fetchLabels();
            if (!success) {
                toast.error('Failed to refresh labels');
            }
        }
    }

    // Initial load of labels
    onMount(async () => {
        const success = await fetchLabels();
        if (!success) {
            toast.error('Failed to load labels');
        }
    });
</script>

<Head
    title={`Dashboard - ${PUBLIC_SITE_NAME}`}
    description="Manage your SaaS directory submissions"
    url={`${PUBLIC_SITE_URL}/dashboard`}
/>

<section class="flex-1 bg-secondary py-8 lg:py-24">
    <div class="container-xl">
        <div class="flex justify-between items-start">
            <Tabs.Root value={currentTab} onValueChange={handleTabChange} class="w-full">
                <div class="flex justify-between items-center">
                    <Tabs.List class="bg-white/10 text-white">
                        <Tabs.Trigger value="sites" class="data-[state=active]:bg-white data-[state=active]:text-black">
                            Sites
                        </Tabs.Trigger>
                        <Tabs.Trigger value="labels" class="data-[state=active]:bg-white data-[state=active]:text-black">
                            Labels
                        </Tabs.Trigger>
                    </Tabs.List>
                </div>

                <div class="mt-8">
                    <Tabs.Content value="sites">
                        <SitesTab {labels} {labelsByGroup} />
                    </Tabs.Content>

                    <Tabs.Content value="labels">
                        <LabelsTab {labels} {labelsByGroup} {isLoading} onLabelsUpdated={fetchLabels} />
                    </Tabs.Content>
                </div>
            </Tabs.Root>
        </div>
    </div>
</section>
