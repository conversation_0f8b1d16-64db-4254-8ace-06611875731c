<script lang="ts">
    import type { Note } from '$lib/types/tables';
    import KanbanCard from './KanbanCard.svelte';

    interface Props {
        title: string;
        status: number;
        notes: Note[];
        onEdit: (note: Note) => void;
        onDelete: (noteId: string) => void;
        onDrop: (noteId: string, newStatus: number) => void;
        onDragStart?: (note: Note) => void;
    }

    let { title, status, notes, onEdit, onDelete, onDrop, onDragStart }: Props = $props();

    let isOver = $state(false);

    function getStatusColor(status: number): string {
        switch (status) {
            case 0: return 'bg-blue-100 text-blue-800';
            case 1: return 'bg-yellow-100 text-yellow-800';
            case 2: return 'bg-green-100 text-green-800';
            case 3: return 'bg-purple-100 text-purple-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    function getStatusIcon(status: number): string {
        switch (status) {
            case 0: return '#icon-clock';
            case 1: return '#icon-eye';
            case 2: return '#icon-check';
            case 3: return '#icon-bookmark';
            default: return '#icon-circle';
        }
    }

    function handleDragOver(event: DragEvent) {
        event.preventDefault();
        event.dataTransfer!.dropEffect = 'move';
        isOver = true;
    }

    function handleDragLeave() {
        isOver = false;
    }

    function handleDrop(event: DragEvent) {
        event.preventDefault();
        isOver = false;

        const noteId = event.dataTransfer?.getData('text/plain');
        if (noteId) {
            onDrop(noteId, status);
        }
    }
</script>

<div class="flex flex-col h-full">
    <div class="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
        <div class="flex items-center gap-2">
            <svg class="h-4 w-4 fill-current text-gray-600">
                <use href={getStatusIcon(status)}></use>
            </svg>
            <h2 class="font-semibold text-gray-900">{title}</h2>
            <span class="px-2 py-1 text-xs font-medium rounded-full {getStatusColor(status)}">
                {notes.length}
            </span>
        </div>
    </div>

    <div
        role="region"
        aria-label="Drop zone for {title} notes"
        ondragover={handleDragOver}
        ondragleave={handleDragLeave}
        ondrop={handleDrop}
        class="flex-1 min-h-[200px] p-2 rounded-lg transition-colors {isOver ? 'bg-blue-50 border-2 border-blue-300 border-dashed' : 'bg-gray-50/50'}"
    >
        <div class="space-y-3">
            {#each notes as note (note.note_id)}
                <KanbanCard
                    {note}
                    {onEdit}
                    {onDelete}
                    {onDragStart}
                />
            {/each}

            {#if notes.length === 0}
                <div class="flex items-center justify-center h-32 text-gray-400">
                    <div class="text-center">
                        <svg class="h-8 w-8 mx-auto mb-2 fill-current">
                            <use href="#icon-plus"></use>
                        </svg>
                        <p class="text-sm">No notes</p>
                    </div>
                </div>
            {/if}
        </div>
    </div>
</div>
