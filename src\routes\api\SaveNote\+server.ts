import { json } from '@sveltejs/kit';
import { saveNote } from '$lib/services/notes';
import type { <PERSON>questHand<PERSON> } from './$types';
import type { SaveNoteRequest } from '$lib/types/requests';
import type { SaveNoteResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const noteRequest = await request.json() as SaveNoteRequest;

        if (!noteRequest.note_title) {
            return json({
                status: "error",
                message: "Note title is required"
            } as SaveNoteResponse, { status: 400 });
        }

        const note = await saveNote(noteRequest.note_id, noteRequest.note_title, noteRequest.note_content, noteRequest.note_files);

        const isUpdate = !!noteRequest.note_id;
        const message = isUpdate ? "Note updated successfully" : "Note created successfully";

        return json({
            status: "success",
            message,
            note
        } as SaveNoteResponse, { status: isUpdate ? 200 : 201 });
    } catch (error) {
        console.error('Unexpected error at SaveNote:', error);
        return json({
            status: "error",
            message: `Unexpected error at SaveNote: ${error instanceof Error ? error.message : 'Unknown error'}`
        } as SaveNoteResponse, { status: 500 });
    }
}
