import { json } from '@sveltejs/kit';
import { getSites } from '$lib/services/sites';
import type { RequestHandler } from './$types';
import type { GetSitesRequest } from '$lib/types/requests';
import type { GetSitesResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const req = await request.json() as GetSitesRequest;
        const { andLabelIds = [], orLabelIds = [], page_num = 1, per_page = 100, sort = 'dr', search = '' } = req;

        const result = await getSites(andLabelIds, orLabelIds, page_num, per_page, sort, search);

        return json({
            status: 'success',
            message: 'Sites retrieved successfully',
            sites: result.sites,
            site_count: result.site_count,
            page_num: result.page_num,
            per_page: result.per_page
        } as GetSitesResponse, { status: 200 });
    } catch (error) {
        console.error('Unexpected error at GetSites:', error);
        return json({
            status: 'error',
            message: `Unexpected error at GetSites: ${error instanceof Error ? error.message : 'Unknown error'}`
        } as GetSitesResponse, { status: 500 });
    }
}
