import { json } from '@sveltejs/kit';
import { deleteNote } from '$lib/services/notes';
import type { RequestHandler } from './$types';
import type { DeleteNoteRequest } from '$lib/types/requests';
import type { DeleteNoteResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const req = await request.json() as DeleteNoteRequest;
        const note_id = req.note_id;
        
        if (!note_id) {
            return json({
                status: "error",
                message: "Note ID is required"
            } as DeleteNoteResponse, { status: 400 });
        }
        
        const success = await deleteNote(note_id);
        
        if (!success) {
            return json({
                status: "error",
                message: "Note not found"
            } as DeleteNoteResponse, { status: 404 });
        }
        
        return json({
            status: "success",
            message: "Note deleted successfully"
        } as DeleteNoteResponse, { status: 200 });
    } catch (err) {
        console.error('Unexpected error at DeleteNote:', err);
        return json({
            status: "error",
            message: `Unexpected error at DeleteNote: ${err instanceof Error ? err.message : 'Unknown error'}`
        } as DeleteNoteResponse, { status: 500 });
    }
}
