export interface Site {
    site_id: string;
    site_name: string;
    site_url: string;
    site_submit_url: string;
    site_actual_submit_url: string;
    site_note: string;
    site_waiting_time: string;
    site_created_at: number;
    site_dr?: number;
    site_dr_updated_at?: number;
    site_visits?: number;
    site_visits_updated_at?: number;
    site_form_fields?: string;
}

export interface Label {
    label_id: string;
    label_name: string;
    label_group: string;
    label_color: string;
    label_order?: number;
}

export interface SiteLabelRel {
    site_id: string;
    label_id: string;
}

export interface Report {
    report_id: string;
    report_website_url: string;
    report_email_address: string;
    report_email_password: string;
    report_created_at: number;
    report_status: number;
    report_password: string;
}

export interface ReportSite {
    report_id: string;
    site_id: string;
    checked: boolean;
    screenshot_urls: string[];
}

export interface FormField {
    report_id: string;
    field_name: string;
    field_value: string;
}

export interface Note {
    note_id: string;
    note_title: string;
    note_content: string;
    note_created_at: number;
    note_files?: string | NoteFile[] | null;
    note_status: number;
}

export interface NoteFile {
    file_url: string;
    file_name: string;
    file_size: number;
    file_type: string;
}

export interface SiteFormField {
    type: string;
    label: string;
    match: string;
    required: boolean;
    selector: string;
    optionSelector?: string;
    maxlength?: string;
    value?: string;
    defaultOption?: string;
    delay?: string;
    pageId: string;
    pageName: string;
    options?: Array<{
        label: string;
        selector: string;
    }>;
}
