import { reportExists } from '$lib/services/reports';
import type { PageServerLoad } from './$types';
import type { Report } from '$lib/types/tables';

export const load: PageServerLoad = async ({ params, cookies }) => {
  const report = await reportExists(params.report_id) as Report | null;

  if (!report) {
    return {
      status: 'error',
      message: 'Report not found'
    }
  }

  // Check if user has valid password cookie for this report
  const reportPasswordCookie = cookies.get(`report_password_${params.report_id}`);
  const isAuthenticated = reportPasswordCookie === report.report_password;

  return {
    status: 'success',
    message: 'Report found',
    report,
    isAuthenticated
  }
}
