<script lang="ts">
    import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
    import Head from "$lib/components/layout/Head.svelte";
    import * as Tabs from '$lib/components/ui/tabs/index';
    import { toast } from 'svelte-sonner';
    import { onMount } from 'svelte';
    import type { GetSitesResponse } from '$lib/types/responses';
    import type { SiteWithLabelIds } from '$lib/types/services';

    // State for URL matching tool
    let inputUrls = $state('');
    let unmatchedUrls = $state('');
    let isProcessing = $state(false);
    let allSites = $state<SiteWithLabelIds[]>([]);
    let isLoading = $state(true);

    // State for JSON validation tool
    let isValidating = $state(false);
    let jsonValidationResults = $state<Array<{
        site_id: string;
        site_name: string;
        site_url: string;
        status: 'valid' | 'invalid' | 'empty';
        error?: string;
        fieldCount?: number;
    }>>([]);

    // Function to extract domain from URL
    function extractDomain(url: string): string {
        try {
            // Add protocol if missing
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                url = 'https://' + url;
            }

            const urlObj = new URL(url);
            let domain = urlObj.hostname;

            // Extract just the main domain and TLD (ignore all subdomains)
            const parts = domain.split('.');
            if (parts.length > 2) {
                // Take just the last two parts (main domain and TLD)
                domain = parts.slice(-2).join('.');
            }

            return domain.toLowerCase();
        } catch (error) {
            // Return original string if it's not a valid URL
            return url.toLowerCase();
        }
    }

    // Function to check if a domain exists in the site URLs
    function domainExists(domain: string, sites: SiteWithLabelIds[]): boolean {
        for (const site of sites) {
            const siteDomain = extractDomain(site.site_url);
            if (siteDomain === domain) {
                return true;
            }
        }
        return false;
    }

    // Function to process URLs
    async function processUrls() {
        if (!inputUrls.trim()) {
            toast.error('Please enter URLs to check');
            return;
        }

        isProcessing = true;

        try {
            // Make sure we have all sites
            if (allSites.length === 0) {
                await fetchAllSites();
            }

            const urls = inputUrls.split('\n').filter(url => url.trim());
            const unmatched: string[] = [];

            for (const url of urls) {
                if (!url.trim()) continue;

                const domain = extractDomain(url.trim());
                if (!domainExists(domain, allSites)) {
                    unmatched.push(url.trim());
                }
            }

            unmatchedUrls = unmatched.join('\n');

            if (unmatched.length === 0) {
                toast.success('All URLs matched with existing sites!');
            } else {
                toast.info(`Found ${unmatched.length} unmatched URLs`);
            }
        } catch (error) {
            console.error('Error processing URLs:', error);
            toast.error('Failed to process URLs');
        } finally {
            isProcessing = false;
        }
    }

    // Function to fetch all sites
    async function fetchAllSites() {
        try {
            isLoading = true;

            const response = await fetch(`/api/GetSites`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    page_num: 1,
                    per_page: 1000 // Get a large number of sites
                })
            });

            const data = await response.json() as GetSitesResponse;
            allSites = data.sites;

            isLoading = false;
        } catch (error) {
            console.error('Error fetching sites:', error);
            toast.error('Failed to fetch sites');
            isLoading = false;
        }
    }

    // JSON validation functions
    async function validateAllFormFieldsJSON() {
        try {
            isValidating = true;
            jsonValidationResults = [];

            const response = await fetch('/api/ValidateFormFieldsJSON', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ onlyApproved: true })
            });

            const data = await response.json();

            if (data.status === 'success') {
                jsonValidationResults = data.results;
                toast.success(`Validation complete. Found ${data.results.filter((r: any) => r.status === 'invalid').length} invalid JSON entries.`);
            } else {
                toast.error(data.message || 'Failed to validate JSON');
            }
        } catch (error) {
            console.error('Error validating JSON:', error);
            toast.error('Failed to validate JSON');
        } finally {
            isValidating = false;
        }
    }

    async function fixInvalidJSON(siteId: string) {
        try {
            const response = await fetch('/api/FixFormFieldsJSON', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ site_id: siteId })
            });

            const data = await response.json();

            if (data.status === 'success') {
                toast.success('JSON fixed successfully');
                // Update the result in the list
                const index = jsonValidationResults.findIndex(r => r.site_id === siteId);
                if (index !== -1) {
                    jsonValidationResults[index].status = 'empty';
                    jsonValidationResults[index].error = undefined;
                    jsonValidationResults[index].fieldCount = 0;
                }
            } else {
                toast.error(data.message || 'Failed to fix JSON');
            }
        } catch (error) {
            console.error('Error fixing JSON:', error);
            toast.error('Failed to fix JSON');
        }
    }

    function getStatusColor(status: string) {
        switch (status) {
            case 'valid': return 'text-green-600 bg-green-100';
            case 'invalid': return 'text-red-600 bg-red-100';
            case 'empty': return 'text-gray-600 bg-gray-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    }

    function getStatusText(status: string) {
        switch (status) {
            case 'valid': return 'Valid';
            case 'invalid': return 'Invalid';
            case 'empty': return 'Empty';
            default: return 'Unknown';
        }
    }

    onMount(async () => {
        await fetchAllSites();
    });
</script>

<Head
    title={`Tools - ${PUBLIC_SITE_NAME}`}
    description="Tools for managing your SaaS directory"
    url={`${PUBLIC_SITE_URL}/dashboard/tools`}
/>

<section class="flex-1 bg-secondary py-8 lg:py-24">
    <div class="container-xl">
        <div class="mb-12">
            <h1 class="text-3xl font-bold tracking-tight text-white md:text-4xl lg:text-5xl">Tools</h1>
        </div>

        <div class="relative overflow-hidden rounded-lg bg-white ring-4 ring-white/25">
            <Tabs.Root value="url-matcher" class="w-full">
                <Tabs.List>
                    <Tabs.Trigger value="url-matcher">URL Matcher</Tabs.Trigger>
                    <Tabs.Trigger value="json-validator">JSON Validator</Tabs.Trigger>
                </Tabs.List>

                <div class="p-6">
                    <Tabs.Content value="url-matcher">
                        <div class="flex flex-col gap-6">
                            <div>
                                <h2 class="text-xl font-semibold mb-4">URL Matcher Tool</h2>
                                <p class="text-gray-700 mb-4">
                                    Paste a list of URLs (one per line) to check if they exist in your site database.
                                    URLs that don't match any existing site will be displayed in the results area.
                                </p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex flex-col">
                                    <label for="input-urls" class="mb-2 font-medium">Input URLs (one per line)</label>
                                    <textarea
                                        id="input-urls"
                                        bind:value={inputUrls}
                                        class="w-full h-64 p-3 border border-gray-300 rounded-md"
                                        placeholder="https://example.com&#10;https://another-site.com&#10;..."
                                    ></textarea>
                                </div>

                                <div class="flex flex-col">
                                    <label for="unmatched-urls" class="mb-2 font-medium">Unmatched URLs</label>
                                    <textarea
                                        id="unmatched-urls"
                                        bind:value={unmatchedUrls}
                                        readonly
                                        class="w-full h-64 p-3 border border-gray-300 rounded-md bg-gray-50"
                                        placeholder="Results will appear here..."
                                    ></textarea>
                                </div>
                            </div>

                            <div class="flex justify-center mt-4">
                                <button
                                    onclick={processUrls}
                                    disabled={isProcessing || isLoading}
                                    class="btn-primary w-fit"
                                >
                                    {#if isProcessing}
                                        <div class="h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                        Processing...
                                    {:else if isLoading}
                                        <div class="h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                        Loading Sites...
                                    {:else}
                                        Check URLs
                                    {/if}
                                </button>
                            </div>
                        </div>
                    </Tabs.Content>

                    <Tabs.Content value="json-validator">
                        <div class="flex flex-col gap-6">
                            <div>
                                <h2 class="text-xl font-semibold mb-4">Form Fields JSON Validator</h2>
                                <p class="text-gray-700 mb-4">
                                    Check site_form_fields entries for sites with "Approved" label for valid JSON format.
                                    This tool will identify any corrupted or invalid JSON data in approved sites.
                                </p>
                            </div>

                            <div class="flex justify-center">
                                <button
                                    onclick={validateAllFormFieldsJSON}
                                    disabled={isValidating}
                                    class="btn-primary w-fit"
                                >
                                    {#if isValidating}
                                        <div class="h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                        Validating...
                                    {:else}
                                        Validate Approved Sites JSON
                                    {/if}
                                </button>
                            </div>

                            {#if jsonValidationResults.length > 0}
                                <div class="mt-6">
                                    <div class="mb-4 flex items-center justify-between">
                                        <h3 class="text-lg font-medium">Validation Results</h3>
                                        <div class="flex gap-4 text-sm">
                                            <span class="text-green-600">
                                                Valid: {jsonValidationResults.filter(r => r.status === 'valid').length}
                                            </span>
                                            <span class="text-red-600">
                                                Invalid: {jsonValidationResults.filter(r => r.status === 'invalid').length}
                                            </span>
                                            <span class="text-gray-600">
                                                Empty: {jsonValidationResults.filter(r => r.status === 'empty').length}
                                            </span>
                                        </div>
                                    </div>

                                    <div class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Site
                                                    </th>
                                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Status
                                                    </th>
                                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Fields
                                                    </th>
                                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Error
                                                    </th>
                                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                        Actions
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                {#each jsonValidationResults as result}
                                                    <tr>
                                                        <td class="px-6 py-4 whitespace-nowrap">
                                                            <div>
                                                                <div class="text-sm font-medium text-gray-900">
                                                                    {result.site_name || 'Unnamed Site'}
                                                                </div>
                                                                <div class="text-sm text-gray-500 truncate max-w-xs">
                                                                    {result.site_url}
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap">
                                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {getStatusColor(result.status)}">
                                                                {getStatusText(result.status)}
                                                            </span>
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                            {result.fieldCount ?? 'N/A'}
                                                        </td>
                                                        <td class="px-6 py-4 text-sm text-red-600 max-w-xs truncate">
                                                            {result.error || ''}
                                                        </td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                            {#if result.status === 'invalid'}
                                                                <button
                                                                    onclick={() => fixInvalidJSON(result.site_id)}
                                                                    class="text-blue-600 hover:text-blue-900"
                                                                >
                                                                    Fix (Set to Empty)
                                                                </button>
                                                            {/if}
                                                        </td>
                                                    </tr>
                                                {/each}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            {/if}
                        </div>
                    </Tabs.Content>
                </div>
            </Tabs.Root>
        </div>
    </div>
</section>
