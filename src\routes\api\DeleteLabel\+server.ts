import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { deleteLabel } from '$lib/services/labels';
import type { DeleteLabelRequest } from '$lib/types/requests';
import type { DeleteLabelResponse } from '$lib/types/responses';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const req = await request.json() as DeleteLabelRequest;
        const label_id = req.label_id;
        
        if (!label_id) {
            return json({
                status: "error",
                message: "Label ID is required"
            } as DeleteLabelResponse, { status: 400 });
        }
        
        await deleteLabel(label_id);
        
        return json({
            status: "success",
            message: "Label deleted successfully"
        } as DeleteLabelResponse, { status: 200 });
    } catch (err) {
        console.error('Unexpected error at DeleteLabel:', err);
        return json({
            status: "error",
            message: `Unexpected error at DeleteLabel: ${err instanceof Error ? err.message : 'Unknown error'}`
        } as DeleteLabelResponse, { status: 500 });
    }
}
