<script lang="ts">
    import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
    import Head from "$lib/components/layout/Head.svelte";
    import * as Dialog from '$lib/components/ui/dialog/index';
    import { onMount } from 'svelte';
    import { toast } from 'svelte-sonner';
    import type { Note, NoteFile } from '$lib/types/tables';
    import type { GetNotesResponse, SaveNoteResponse, DeleteNoteResponse } from '$lib/types/responses';
    import FileUpload from '$lib/components/notes/FileUpload.svelte';
    import KanbanBoard from '$lib/components/notes/KanbanBoard.svelte';
    import { parseNoteFiles } from '$lib/utils/fileUtils';

    let notes = $state<Note[]>([]);
    let isLoading = $state(true);
    let createDialogOpen = $state(false);
    let editDialogOpen = $state(false);
    let editingNote = $state<Note | null>(null);

    let newNote = $state({
        note_title: '',
        note_content: ''
    });

    let editNote = $state({
        note_title: '',
        note_content: ''
    });

    let newNoteFiles = $state<NoteFile[]>([]);
    let editNoteFiles = $state<NoteFile[]>([]);

    async function fetchNotes(): Promise<void> {
        try {
            const response = await fetch('/api/GetNotes', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json() as GetNotesResponse;

            if (data.status === 'success') {
                notes = data.notes;
            } else {
                console.error('Failed to fetch notes:', data.message);
                toast.error('Failed to load notes');
            }
        } catch (error) {
            console.error('Error fetching notes:', error);
            toast.error('Failed to load notes');
        } finally {
            isLoading = false;
        }
    }

    async function createNote() {
        if (!newNote.note_title.trim()) {
            toast.error('Please fill in the title');
            return;
        }

        try {
            const response = await fetch('/api/SaveNote', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    note_title: newNote.note_title.trim(),
                    note_content: newNote.note_content.trim(),
                    note_files: JSON.stringify(newNoteFiles)
                })
            });

            const data = await response.json() as SaveNoteResponse;

            if (data.status === 'success') {
                toast.success(data.message || 'Note created successfully');
                newNote.note_title = '';
                newNote.note_content = '';
                newNoteFiles = [];
                createDialogOpen = false;
                await fetchNotes();
            } else {
                toast.error(data.message || 'Failed to create note');
            }
        } catch (error) {
            console.error('Error creating note:', error);
            toast.error('Failed to create note');
        }
    }

    async function updateNote() {
        if (!editingNote || !editNote.note_title.trim()) {
            toast.error('Please fill in the title');
            return;
        }

        try {
            const response = await fetch('/api/SaveNote', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    note_id: editingNote.note_id,
                    note_title: editNote.note_title.trim(),
                    note_content: editNote.note_content.trim(),
                    note_files: JSON.stringify(editNoteFiles)
                })
            });

            const data = await response.json() as SaveNoteResponse;

            if (data.status === 'success') {
                toast.success(data.message || 'Note updated successfully');
                editDialogOpen = false;
                editingNote = null;
                await fetchNotes();
            } else {
                toast.error(data.message || 'Failed to update note');
            }
        } catch (error) {
            console.error('Error updating note:', error);
            toast.error('Failed to update note');
        }
    }

    async function handleDeleteNote(noteId: string) {
        if (!confirm('Are you sure you want to delete this note?')) {
            return;
        }

        try {
            const response = await fetch('/api/DeleteNote', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ note_id: noteId })
            });

            const data = await response.json() as DeleteNoteResponse;

            if (data.status === 'success') {
                toast.success(data.message || 'Note deleted successfully');
                await fetchNotes();
            } else {
                toast.error(data.message || 'Failed to delete note');
            }
        } catch (error) {
            console.error('Error deleting note:', error);
            toast.error('Failed to delete note');
        }
    }

    function openEditDialog(note: Note) {
        editingNote = note;
        editNote.note_title = note.note_title;
        editNote.note_content = note.note_content;
        editNoteFiles = parseNoteFiles(note.note_files);
        editDialogOpen = true;
    }

    onMount(async () => {
        await fetchNotes();
    });
</script>

<Head
    title={`Notes - ${PUBLIC_SITE_NAME}`}
    description="Manage your notes"
    url={`${PUBLIC_SITE_URL}/dashboard/notes`}
/>

<section class="flex-1 bg-secondary py-8 lg:py-24">
    <div class="container-xl">
        <div class="mb-12 flex items-center justify-between">
            <h1 class="text-3xl font-bold tracking-tight text-white md:text-4xl">Notes</h1>
            <button
                onclick={() => {
                    newNote.note_title = '';
                    newNote.note_content = '';
                    newNoteFiles = [];
                    createDialogOpen = true;
                }}
                class="btn-primary w-fit"
            >
                <svg class="h-4 w-4 fill-white">
                    <use href="#icon-plus"></use>
                </svg>
                New
            </button>
        </div>

        {#if isLoading}
            <div class="flex h-40 items-center justify-center">
                <div class="flex items-center justify-center gap-2">
                    <div class="h-8 w-8 animate-spin rounded-full border-2 border-gray-200 border-t-gray-400"></div>
                    <span class="font-semibold text-white">Loading...</span>
                </div>
            </div>
        {:else if notes.length === 0}
            <div class="flex h-40 items-center justify-center">
                <p class="text-white text-lg">No notes yet. Create your first note!</p>
            </div>
        {:else}
            <KanbanBoard
                {notes}
                onEdit={openEditDialog}
                onDelete={handleDeleteNote}
                onNotesUpdate={fetchNotes}
            />
        {/if}
    </div>
</section>

<!-- Create Note Dialog -->
<Dialog.Root bind:open={createDialogOpen}>
    <Dialog.Content class="sm:max-w-[600px] max-h-[90vh]">
        <Dialog.Header>
            <Dialog.Title>Create New Note</Dialog.Title>
            <Dialog.Description>
                Add a new note to your collection.
            </Dialog.Description>
        </Dialog.Header>
        <div class="grid gap-4 py-4">
            <div class="grid gap-2">
                <label for="new-title" class="text-sm font-medium">Title</label>
                <input
                    id="new-title"
                    bind:value={newNote.note_title}
                    placeholder="Enter note title..."
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
            </div>
            <div class="grid gap-2">
                <label for="new-content" class="text-sm font-medium">Content (optional)</label>
                <textarea
                    id="new-content"
                    bind:value={newNote.note_content}
                    placeholder="Enter note content (optional)..."
                    rows="12"
                    class="flex min-h-[200px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                ></textarea>
            </div>
            <FileUpload bind:files={newNoteFiles} />
        </div>
        <Dialog.Footer>
            <button
                onclick={() => createDialogOpen = false}
                class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
            >
                Cancel
            </button>
            <button
                onclick={createNote}
                class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
                Create Note
            </button>
        </Dialog.Footer>
    </Dialog.Content>
</Dialog.Root>

<!-- Edit Note Dialog -->
<Dialog.Root bind:open={editDialogOpen}>
    <Dialog.Content class="sm:max-w-[600px] max-h-[90vh]">
        <Dialog.Header>
            <Dialog.Title>Edit Note</Dialog.Title>
            <Dialog.Description>
                Make changes to your note.
            </Dialog.Description>
        </Dialog.Header>
        <div class="grid gap-4 py-4">
            <div class="grid gap-2">
                <label for="edit-title" class="text-sm font-medium">Title</label>
                <input
                    id="edit-title"
                    bind:value={editNote.note_title}
                    placeholder="Enter note title..."
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
            </div>
            <div class="grid gap-2">
                <label for="edit-content" class="text-sm font-medium">Content (optional)</label>
                <textarea
                    id="edit-content"
                    bind:value={editNote.note_content}
                    placeholder="Enter note content (optional)..."
                    rows="12"
                    class="flex min-h-[200px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                ></textarea>
            </div>
            <FileUpload bind:files={editNoteFiles} />
        </div>
        <Dialog.Footer>
            <button
                onclick={() => editDialogOpen = false}
                class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
            >
                Cancel
            </button>
            <button
                onclick={updateNote}
                class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
                Update Note
            </button>
        </Dialog.Footer>
    </Dialog.Content>
</Dialog.Root>
