import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { updateNoteStatus } from '$lib/services/notes';
import type { BaseResponse } from '$lib/types/responses';

interface UpdateNoteStatusRequest {
    note_id: string;
    note_status: number;
}

interface UpdateNoteStatusResponse extends BaseResponse {
    note?: any;
}

export const POST: RequestHandler = async ({ request }) => {
    try {
        const { note_id, note_status }: UpdateNoteStatusRequest = await request.json();

        if (!note_id || note_status === undefined || note_status === null) {
            return json<UpdateNoteStatusResponse>({
                status: 'error',
                message: 'Note ID and status are required'
            }, { status: 400 });
        }

        if (![0, 1, 2, 3].includes(note_status)) {
            return json<UpdateNoteStatusResponse>({
                status: 'error',
                message: 'Invalid note status. Must be 0 (TO-DO), 1 (TO-REVIEW), 2 (DONE), or 3 (SAVED)'
            }, { status: 400 });
        }

        const updatedNote = await updateNoteStatus(note_id, note_status);

        if (!updatedNote) {
            return json<UpdateNoteStatusResponse>({
                status: 'error',
                message: 'Note not found'
            }, { status: 404 });
        }

        return json<UpdateNoteStatusResponse>({
            status: 'success',
            message: 'Note status updated successfully',
            note: updatedNote
        });

    } catch (error) {
        console.error('Error updating note status:', error);
        return json<UpdateNoteStatusResponse>({
            status: 'error',
            message: 'Failed to update note status'
        }, { status: 500 });
    }
};
