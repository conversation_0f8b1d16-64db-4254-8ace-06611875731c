import { PUBLIC_FILES_FOLDER } from '$lib/utils/env';
import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import type { NoteFile } from '$lib/types/tables';

/**
 * Extracts filename from a file URL
 * @param fileUrl The full file URL (e.g., "http://localhost:5173/files/uuid.pdf")
 * @returns The filename or null if invalid
 */
export function extractFilenameFromUrl(fileUrl: string): string | null {
    if (!fileUrl) return null;

    const urlParts = fileUrl.split('/files/');
    if (urlParts.length !== 2) return null;

    const fileName = urlParts[1];

    // Validate filename (should be UUID + extension, no path traversal)
    if (!fileName || fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
        return null;
    }

    return fileName;
}

/**
 * Deletes a single file from the file system
 * @param fileUrl The file URL to delete
 * @returns Promise<boolean> - true if deleted successfully, false otherwise
 */
export async function deleteFileFromSystem(fileUrl: string): Promise<boolean> {
    try {
        const fileName = extractFilenameFromUrl(fileUrl);
        if (!fileName) {
            console.error('Invalid file URL:', fileUrl);
            return false;
        }

        const filePath = path.join(PUBLIC_FILES_FOLDER, fileName);

        try {
            // Check if file exists
            await fs.access(filePath);

            // Delete the file
            await fs.unlink(filePath);

            console.log('File deleted successfully:', fileName);
            return true;

        } catch (fileError: any) {
            if (fileError.code === 'ENOENT') {
                // File doesn't exist, consider it already deleted
                console.log('File already deleted or does not exist:', fileName);
                return true;
            } else {
                console.error('Error deleting file:', fileError);
                return false;
            }
        }

    } catch (error) {
        console.error('Error in deleteFileFromSystem:', error);
        return false;
    }
}

/**
 * Deletes multiple files from the file system
 * @param files Array of NoteFile objects to delete
 * @returns Promise<number> - number of files successfully deleted
 */
export async function deleteFilesFromSystem(files: NoteFile[]): Promise<number> {
    if (!files || files.length === 0) return 0;

    let deletedCount = 0;

    for (const file of files) {
        const success = await deleteFileFromSystem(file.file_url);
        if (success) {
            deletedCount++;
        }
    }

    return deletedCount;
}

/**
 * Safely parses note files JSON and returns array of NoteFile objects
 * @param noteFiles The note_files field from database (can be string, array, or null)
 * @returns Array of NoteFile objects
 */
export function parseNoteFiles(noteFiles: any): NoteFile[] {
    // Handle null, undefined, or falsy values
    if (!noteFiles) {
        return [];
    }

    // If it's already an array, return it
    if (Array.isArray(noteFiles)) {
        return noteFiles;
    }

    // If it's a string, try to parse it
    if (typeof noteFiles === 'string') {
        // Handle empty strings
        const trimmed = noteFiles.trim();
        if (trimmed === '' || trimmed === 'null' || trimmed === 'undefined') {
            return [];
        }

        try {
            const parsed = JSON.parse(trimmed);
            return Array.isArray(parsed) ? parsed : [];
        } catch (error) {
            console.error('Error parsing note files JSON:', error, 'Input was:', noteFiles);
            return [];
        }
    }

    // If it's an object but not an array, return empty array
    if (typeof noteFiles === 'object') {
        return [];
    }

    // For any other type, return empty array
    return [];
}
