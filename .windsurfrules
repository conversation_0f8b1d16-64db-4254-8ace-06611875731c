# General

- I'm using SvelteKit 2 and <PERSON>vel<PERSON> 5
- I'm using Bun
- Don't write comments
- The DB is:

CREATE TABLE sites (
  site_id UUID NOT NULL PRIMARY KEY,
  site_name VARCHAR(255) NOT NULL DEFAULT '',
  site_url TEXT NOT NULL UNIQUE,
  site_submit_url TEXT NOT NULL DEFAULT '',
  site_actual_submit_url TEXT NOT NULL DEFAULT '',
  site_note TEXT NOT NULL DEFAULT '',
  site_waiting_time TEXT NOT NULL DEFAULT '',
  site_created_at BIGINT NOT NULL DEFAULT 0,
  site_dr DECIMAL(4, 1) NOT NULL DEFAULT 0.0,
  site_dr_updated_at BIGINT NOT NULL DEFAULT 0,
  site_visits BIGINT NOT NULL DEFAULT -1,
  site_visits_updated_at BIGINT NOT NULL DEFAULT 0,
  site_form_fields JSON NOT NULL DEFAULT '{}'
);

CREATE INDEX idx_sites_site_name ON sites (site_name);

CREATE TABLE labels (
  label_id UUID NOT NULL PRIMARY KEY,
  label_name VA<PERSON>HAR(255) NOT NULL,
  label_group VARCHAR(255) NOT NULL DEFAULT '',
  label_color VARCHAR(7) NOT NULL DEFAULT '#000000',
  label_order INTEGER NOT NULL DEFAULT 0
);

CREATE TABLE site_label_rels (
  site_id UUID NOT NULL,
  label_id UUID NOT NULL,
  PRIMARY KEY (site_id, label_id),
  CONSTRAINT fk_site_label_rels_site_id FOREIGN KEY (site_id) REFERENCES sites (site_id) ON DELETE CASCADE,
  CONSTRAINT fk_site_label_rels_label_id FOREIGN KEY (label_id) REFERENCES labels (label_id) ON DELETE CASCADE
);

CREATE INDEX idx_site_label_rels_label_id_site_id ON site_label_rels (label_id, site_id);

CREATE TABLE reports (
  report_id UUID NOT NULL PRIMARY KEY,
  report_website_url TEXT NOT NULL DEFAULT '',
  report_email_address VARCHAR(255) NOT NULL DEFAULT '',
  report_email_password VARCHAR(255) NOT NULL DEFAULT '',
  report_created_at BIGINT NOT NULL DEFAULT 0,
  report_status INTEGER NOT NULL DEFAULT 0,
  report_password VARCHAR(255) NOT NULL DEFAULT ''
);

CREATE TABLE report_sites (
  report_id UUID NOT NULL,
  site_id UUID NOT NULL,
  checked BOOLEAN NOT NULL DEFAULT FALSE,
  screenshot_urls TEXT[] NOT NULL DEFAULT '{}',
  PRIMARY KEY (report_id, site_id),
  CONSTRAINT fk_report_sites_report_id FOREIGN KEY (report_id) REFERENCES reports (report_id) ON DELETE CASCADE,
  CONSTRAINT fk_report_sites_site_id FOREIGN KEY (site_id) REFERENCES sites (site_id) ON DELETE CASCADE
);

CREATE INDEX idx_report_sites_site_id ON report_sites (site_id);

CREATE TABLE form_fields (
  report_id UUID NOT NULL,
  field_name VARCHAR(255) NOT NULL DEFAULT '',
  field_value TEXT NOT NULL DEFAULT '',
  PRIMARY KEY (report_id, field_name),
  CONSTRAINT fk_form_fields_report FOREIGN KEY (report_id) REFERENCES reports (report_id) ON DELETE CASCADE
);

CREATE INDEX idx_form_fields_field_name ON form_fields (field_name);

CREATE TABLE IF NOT EXISTS notes (
  note_id UUID NOT NULL PRIMARY KEY,
  note_title VARCHAR(255) NOT NULL DEFAULT '',
  note_content TEXT NOT NULL DEFAULT '',
  note_created_at BIGINT NOT NULL DEFAULT 0,
  note_files JSON NOT NULL DEFAULT '[]',
  note_status INTEGER NOT NULL DEFAULT 0
);