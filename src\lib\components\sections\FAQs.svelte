<script lang="ts">
    // Define the FAQ items as an array of objects
    const faqItems = [
        {
          question: "What is SubmitSaaS?",
          answer: "SubmitSaaS is a service designed to amplify brand visibility, drive relevant traffic, and reach clients by listing your SaaS in 60+ quality directories, building quality backlinks to improve SEO."
        },
        {
          question: "Why is a dedicated email address created for the submission process?",
          answer: "We create a dedicated email address to manage all directory sign-ups, confirmations, and communications efficiently on your behalf. This keeps your primary business inbox uncluttered and centralizes all submission-related activity. You will receive full access credentials for this dedicated email along with your final report."
        },
        {
          question: "Do you use automation for submissions?",
          answer: "Yes! We use AI-powered automation to handle submissions efficiently while maintaining accuracy and quality. Our system ensures your SaaS is listed seamlessly across 100+ directories."
        },
        {
          question: "What kind of reports do you provide after submission?",
          answer: "Your submission results are tracked in a Google Sheet, including detailed reports and verification screenshots."
        },
        {
          question: "How soon will I see results?",
          answer: "Results vary by SaaS, but you can typically expect increased traffic, improved SEO, and more potential customers within 30 days of completing your directory submissions."
        },
        {
          question: "How long until directories list my SaaS?",
          answer: "Approval times vary by directory: some list your SaaS immediately, while others may take days, weeks, or even months to review and publish."
        },
        {
          question: "Do you handle submissions to Product Hunt?",
          answer: "Currently, we are not including submissions to Product Hunt."
        },
        {
          question: "What kind of DR boost can I expect from your packages?",
          answer: "We can't guarantee a specific DR increase because each website is unique. Still, our service is made to create a solid base that boosts your DR. Each package submits your site to a different number of directories, and our largest package (100+ directories) aims for the biggest DR improvement. We provide the exact same core service as many competitors, focusing on quality directory submissions, but unlike some who promise dramatic, unrealistic DR jumps, we prioritize genuine, quality-driven link-building that truly enhances your domain authority."
        },
        {
          question: "If my site's DR is above 10, will I still benefit, and can I expect my DR to increase?",
          answer: "This service is best suited for newer sites or those with a DR below 5. You may still see an increase in traffic, but the impact on your Domain Rating itself may be less significant."
        }
    ];

    // Reactive state to track open items (initialized to all closed)
    let openItems = Array(faqItems.length).fill(false);

    // Function to toggle the state of an item
    function toggleFAQ(index: number) {
        openItems[index] = !openItems[index];
    }
</script>

<section id="section-faqs" class="py-8 lg:py-24 bg-white">
    <div class="container">
        <h2 class="max-w-3xl mx-auto mb-12 text-4xl font-bold text-center text-black lg:text-5xl">
            Frequently Asked Questions
        </h2>
        <div id="home-faqs" class="flex flex-col max-w-[800px] mx-auto gap-4">
            {#each faqItems as item, index}
                <div class="border border-black rounded-lg">
                    <button
                        type="button"
                        class="flex items-center justify-between w-full gap-3 p-5 font-semibold text-black lg:pl-8"
                        onclick={() => toggleFAQ(index)}
                        aria-expanded={openItems[index]}
                    >
                        <span class="flex items-center text-lg text-left lg:text-2xl">{item.question}</span>
                        <svg
                            class="w-5 h-5 fill-current shrink-0 transition-transform duration-200 ease-in-out {openItems[index] ? 'rotate-180' : 'rotate-0'}"
                            aria-hidden="true"
                        >
                            <use href="#icon-chevron-down" />
                        </svg>
                    </button>
                    <div
                        class="p-5 pt-0 lg:p-8 lg:pt-0 overflow-hidden {!openItems[index] ? 'hidden' : 'block'}"
                    >
                        <p class="text-gray-600 sm:text-lg">{item.answer}</p>
                    </div>
                </div>
            {/each}
        </div>
    </div>
</section>
